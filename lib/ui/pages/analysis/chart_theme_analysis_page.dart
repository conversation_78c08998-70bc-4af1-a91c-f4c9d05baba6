import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../models/birth_data.dart';
import '../../../models/chart_data.dart';
import '../../../models/chart_type.dart';
import '../../../viewmodels/chart_viewmodel.dart';
import '../../../widgets/chart_theme_analysis_widget.dart';
import '../../../widgets/styled_card.dart';
import '../../AppTheme.dart';
import '../chart_page.dart';
import '../main/files_page.dart';

/// 星盤主題分析頁面
/// 
/// 提供完整的主題分析功能，包括出生資料選擇和七大主題分析
class ChartThemeAnalysisPage extends StatefulWidget {
  const ChartThemeAnalysisPage({Key? key}) : super(key: key);

  @override
  State<ChartThemeAnalysisPage> createState() => _ChartThemeAnalysisPageState();
}

class _ChartThemeAnalysisPageState extends State<ChartThemeAnalysisPage> {
  BirthData? _selectedBirthData;
  ChartViewModel? _chartViewModel;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('星盤主題分析'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: _selectedBirthData == null
          ? _buildBirthDataSelection()
          : _buildThemeAnalysis(),
    );
  }

  /// 建構出生資料選擇界面
  Widget _buildBirthDataSelection() {
    return Column(
      children: [
        // 說明區域
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.royalIndigo.withOpacity(0.1),
                AppColors.solarAmber.withOpacity(0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.royalIndigo.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.psychology,
                    color: AppColors.royalIndigo,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '星盤主題分析',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                '深度解析您的星盤，提供七大主題的專業分析：',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[700],
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 12),
              _buildThemeList(),
              const SizedBox(height: 16),
              Text(
                '請選擇要分析的出生資料：',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
        ),

        // 出生資料選擇按鈕
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _selectBirthData,
              icon: const Icon(Icons.person_search),
              label: const Text('選擇出生資料'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                textStyle: const TextStyle(fontSize: 16),
              ),
            ),
          ),
        ),

        const SizedBox(height: 20),
      ],
    );
  }

  /// 建構主題列表
  Widget _buildThemeList() {
    final themes = [
      '• 命盤核心架構（性格與天賦）',
      '• 情感與親密關係',
      '• 職涯方向與人生使命',
      '• 心理療癒與潛意識模式',
      '• 流年與推運階段分析',
      '• 決策與問題討論',
      '• 占星諮詢的進階風格差異',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: themes.map((theme) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            theme,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 建構主題分析界面
  Widget _buildThemeAnalysis() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在計算星盤數據...'),
          ],
        ),
      );
    }

    if (_chartViewModel == null) {
      return const Center(
        child: Text('星盤數據載入失敗'),
      );
    }

    return Column(
      children: [
        // 當前選擇的出生資料信息
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.royalIndigo.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.royalIndigo.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.person,
                color: AppColors.royalIndigo,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedBirthData!.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${_selectedBirthData!.birthDate.year}年${_selectedBirthData!.birthDate.month}月${_selectedBirthData!.birthDate.day}日 ${_selectedBirthData!.birthDate.hour.toString().padLeft(2, '0')}:${_selectedBirthData!.birthDate.minute.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      _selectedBirthData!.birthPlace,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedBirthData = null;
                    _chartViewModel = null;
                  });
                },
                child: const Text('重新選擇'),
              ),
            ],
          ),
        ),

        // 主題分析組件
        Expanded(
          child: ChangeNotifierProvider.value(
            value: _chartViewModel!,
            child: ChartThemeAnalysisWidget(
              viewModel: _chartViewModel!,
            ),
          ),
        ),
      ],
    );
  }

  /// 選擇出生資料
  Future<void> _selectBirthData() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FilesPage(
          isSelectionMode: true,
        ),
      ),
    );

    if (result is BirthData) {
      setState(() {
        _selectedBirthData = result;
        _isLoading = true;
      });

      // 創建星盤數據
      final chartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: result,
      );

      // 創建 ChartViewModel 並計算星盤
      final chartViewModel = ChartViewModel.withChartData(
        initialChartData: chartData,
      );

      try {
        await chartViewModel.calculateChart();
        
        setState(() {
          _chartViewModel = chartViewModel;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('星盤計算失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
