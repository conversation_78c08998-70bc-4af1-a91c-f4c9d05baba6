import 'package:flutter/material.dart';

import '../../../ui/AppTheme.dart';
import '../../../widgets/styled_card.dart';
import '../analysis/chart_theme_analysis_page.dart';
import '../analysis/divination_analysis_page.dart';
import '../analysis/relationship_analysis_page.dart';
import '../astro_calendar_page.dart';
import '../equinox_solstice_page.dart';

class AnalysisPage extends StatefulWidget {
  const AnalysisPage({super.key});

  @override
  State<AnalysisPage> createState() => _AnalysisPageState();
}

class _AnalysisPageState extends State<AnalysisPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('星盤分析'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionHeader('心理探索與自我成長', Icons.psychology),
          _buildPsychologySection(),
          const SizedBox(height: 24),

          _buildSectionHeader('感情與人際關係', Icons.favorite),
          _buildRelationshipSection(),
          const SizedBox(height: 24),

          _buildSectionHeader('事業與金錢規劃', Icons.work),
          _buildCareerSection(),
          const SizedBox(height: 24),

          _buildSectionHeader('流年運勢與趨勢預測', Icons.timeline),
          _buildYearlyForecastSection(),
          const SizedBox(height: 24),

          _buildSectionHeader('親子與家庭關係', Icons.family_restroom),
          _buildFamilySection(),
          const SizedBox(height: 24),

          _buildSectionHeader('天象與節氣分析', Icons.wb_sunny),
          _buildSeasonalSection(),
          const SizedBox(height: 24),

          _buildSectionHeader('星象日曆', Icons.calendar_month),
          _buildCalendarSection(),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  /// 構建章節標題
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: AppColors.royalIndigo, size: 28),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.royalIndigo,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建分析選項卡片
  Widget _buildAnalysisCard(String title, String description, IconData icon, {Color? color, bool navigateToDetail = false, bool isFortuneTelling = false, bool isEquinoxSolstice = false, bool isAstroCalendar = false, bool isThemeAnalysis = false}) {
    return StyledCard(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      borderRadius: BorderRadius.circular(12),
      onTap: () {
        if (isEquinoxSolstice) {
          _navigateToEquinoxSolstice();
        } else if (isAstroCalendar) {
          _navigateToAstroCalendar();
        } else if (isThemeAnalysis) {
          _navigateToThemeAnalysis();
        } else if (navigateToDetail) {
          _navigateToAnalysisDetail(title, description, icon, color, isFortuneTelling: isFortuneTelling);
        } else {
          _showFeatureNotAvailable(title);
        }
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            backgroundColor: color ?? AppColors.solarAmber,
            radius: 24,
            child: Icon(icon, color: Colors.white, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: const TextStyle(fontSize: 14, color: Colors.black87),
                ),
                  ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
          ],
        ),
    );
  }

  /// 導航到分析詳情頁面
  void _navigateToAnalysisDetail(String title, String description, IconData icon, Color? color, {bool isFortuneTelling = false}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => isFortuneTelling
            ? DivinationAnalysisPage(
                title: title,
                description: description,
              )
            : RelationshipAnalysisPage(
                title: title,
                description: description,
              ),
      ),
    );
  }

  /// 顯示功能尚未開放的提示
  void _showFeatureNotAvailable(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature功能即將推出，敬請期待！'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 構建感情與人際關係部分
  Widget _buildRelationshipSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '戀愛發展分析',
          '什麼時候會遇到對象？對方特質？進展如何？',
          Icons.favorite_border,
          color: Colors.pinkAccent,
          navigateToDetail: true,
        ),
        _buildAnalysisCard(
          '感情合盤分析',
          '與特定對象是否契合？優勢與挑戰？',
          Icons.people_outline,
          color: Colors.purpleAccent,
        ),
        _buildAnalysisCard(
          '斷聯與復合分析',
          '對方是否還有感情？何時可能聯絡？',
          Icons.phone_missed,
          color: Colors.redAccent,
        ),
        _buildAnalysisCard(
          '婚姻走向預測',
          '婚姻機運時間點、適合共度一生的人格特質',
          Icons.diamond_outlined,
          color: Colors.blueAccent,
        ),
        _buildAnalysisCard(
          '人際溝通與職場互動',
          '與同事、主管、家人如何相處？',
          Icons.connect_without_contact,
          color: Colors.teal,
        ),
      ],
    );
  }

  /// 構建事業與金錢規劃部分
  Widget _buildCareerSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '天賦與職涯分析',
          '適合的職業領域、工作風格與學習方式',
          Icons.lightbulb_outline,
          color: Colors.amberAccent,
        ),
        _buildAnalysisCard(
          '創業與轉職時機',
          '什麼時候行動較順利？有貴人相助的流年？',
          Icons.trending_up,
          color: Colors.green,
        ),
        _buildAnalysisCard(
          '財務狀況與投資傾向',
          '金錢觀念、理財風格、財運高峰期',
          Icons.attach_money,
          color: Colors.greenAccent,
        ),
        _buildAnalysisCard(
          '升遷與目標規劃',
          '是否適合爭取升遷？轉換跑道的風險評估',
          Icons.emoji_events_outlined,
          color: Colors.orangeAccent,
        ),
      ],
    );
  }

  /// 構建心理探索與自我成長部分
  Widget _buildPsychologySection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '星盤主題分析',
          '七大主題深度解析：性格天賦、情感關係、職涯使命、心理療癒等',
          Icons.psychology,
          color: AppColors.royalIndigo,
          navigateToDetail: true,
          isThemeAnalysis: true,
        ),
        _buildAnalysisCard(
          '卜卦分析',
          '占星卜卦、周易卜卦和塔羅牌占卜',
          Icons.auto_awesome,
          color: AppColors.solarAmber,
          navigateToDetail: true,
          isFortuneTelling: true,
        ),
        _buildAnalysisCard(
          '本命盤深度解析',
          '人格特質、內在矛盾、潛在動力',
          Icons.person_outline,
          color: Colors.indigoAccent,
        ),
        _buildAnalysisCard(
          '內在課題與療癒方向',
          '童年傷痕、依附風格、心靈平衡策略',
          Icons.healing,
          color: Colors.deepPurpleAccent,
        ),
        _buildAnalysisCard(
          '靈性發展與生命目的',
          '南北交點、業力指向、靈魂學習主題',
          Icons.auto_awesome,
          color: Colors.purpleAccent,
        ),
        _buildAnalysisCard(
          'Saturn Return與轉折期解讀',
          '大運與轉化階段的支持',
          Icons.change_circle_outlined,
          color: Colors.blueGrey,
        ),
      ],
    );
  }

  /// 構建流年運勢與趨勢預測部分
  Widget _buildYearlyForecastSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '年度運勢分析',
          '流年、次限、法達等：人生重點領域會落在哪？',
          Icons.calendar_today,
          color: Colors.blue,
        ),
        _buildAnalysisCard(
          '月運與每日星象指引',
          '短期行動建議與情緒準備',
          Icons.today,
          color: Colors.cyan,
        ),
        _buildAnalysisCard(
          '重大事件預測與避險建議',
          '適合出國、結婚、投資、手術等時機',
          Icons.event_available,
          color: Colors.deepOrange,
        ),
        _buildAnalysisCard(
          '水逆、蝕季與星象敏感期提醒',
          '避免重大決策與情緒反覆期',
          Icons.warning_amber,
          color: Colors.amber,
        ),
      ],
    );
  }

  /// 構建親子與家庭關係部分
  Widget _buildFamilySection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '孩子本命分析',
          '天賦潛能、學習風格與教養建議',
          Icons.child_care,
          color: Colors.lightBlueAccent,
        ),
        _buildAnalysisCard(
          '親子合盤',
          '與父母或孩子的互動模式、教養衝突來源',
          Icons.family_restroom,
          color: Colors.teal,
        ),
        _buildAnalysisCard(
          '家庭系統與祖先業力',
          '星盤中的代際課題',
          Icons.account_tree,
          color: Colors.brown,
        ),
      ],
    );
  }

  /// 構建天象與節氣分析部分
  Widget _buildSeasonalSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '二分二至圖',
          '春分、夏至、秋分、冬至四個節氣的星盤配置與個人影響',
          Icons.wb_sunny,
          color: AppColors.solarAmber,
          navigateToDetail: true,
          isEquinoxSolstice: true,
        ),
        _buildAnalysisCard(
          '月相與節氣影響',
          '每月月相變化對個人情緒與能量的影響',
          Icons.nightlight_round,
          color: Colors.indigo,
        ),
        _buildAnalysisCard(
          '行星逆行影響',
          '水逆、金逆、火逆等行星逆行對個人的影響',
          Icons.refresh,
          color: Colors.deepOrange,
        ),
        _buildAnalysisCard(
          '日月蝕影響',
          '日蝕與月蝕對個人生命主題的影響',
          Icons.brightness_2,
          color: Colors.purple,
        ),
      ],
    );
  }

  /// 構建星象日曆部分
  Widget _buildCalendarSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '星象日曆',
          '查看每月的重要星象事件，包括月相、節氣、行星相位和換座',
          Icons.calendar_month,
          color: AppColors.royalIndigo,
          navigateToDetail: true,
          isAstroCalendar: true,
        ),
        _buildAnalysisCard(
          '個人化星象提醒',
          '根據個人本命盤設置重要星象事件的提醒通知',
          Icons.notifications_active,
          color: Colors.amber,
        ),
        _buildAnalysisCard(
          '星象統計分析',
          '分析個人在不同星象週期中的運勢趨勢',
          Icons.analytics,
          color: Colors.teal,
        ),
      ],
    );
  }

  /// 導航到二分二至圖頁面
  void _navigateToEquinoxSolstice() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const EquinoxSolsticePage(),
      ),
    );
  }

  /// 導航到星象日曆頁面
  void _navigateToAstroCalendar() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AstroCalendarPage(),
      ),
    );
  }

  /// 導航到主題分析頁面
  void _navigateToThemeAnalysis() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ChartThemeAnalysisPage(),
      ),
    );
  }
}
