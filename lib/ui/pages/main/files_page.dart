import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../models/birth_data.dart';
import '../../../models/chart_data.dart';
import '../../../models/chart_type.dart';
import '../../../ui/AppTheme.dart';
import '../../../utils/csv_helper.dart';
import '../../../utils/logger_utils.dart';
import '../../../viewmodels/chart_viewmodel.dart';
import '../../../viewmodels/files_viewmodel.dart';
import '../../../widgets/styled_card.dart';
import '../birth_data_form_page.dart';
import '../chart_page.dart';
import '../chart_selection_page.dart';

class FilesPage extends StatefulWidget {
  final bool isSelectionMode;

  const FilesPage({
    super.key,
    this.isSelectionMode = false,
  });

  @override
  State<FilesPage> createState() => _FilesPageState();
}

class _FilesPageState extends State<FilesPage> {
  late FilesViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    // 在 initState 中獲取 ViewModel
    _viewModel = Provider.of<FilesViewModel>(context, listen: false);
  }

  // 顯示排序選項對話框
  void _showSortOptionsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
          title: const Text('排序方式', style: TextStyle(fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 姓名分組
                _buildSortGroupTitle('姓名'),
                _buildSortOption(
                  title: '升序 (A-Z)',
                  icon: Icons.arrow_upward,
                  isSelected: _viewModel.currentSortOption == SortOption.nameAsc,
                  onTap: () async {
                    await _viewModel.setSortOption(SortOption.nameAsc);
                    Navigator.pop(context);
                  },
                ),
                _buildSortOption(
                  title: '降序 (Z-A)',
                  icon: Icons.arrow_downward,
                  isSelected: _viewModel.currentSortOption == SortOption.nameDesc,
                  onTap: () async {
                    await _viewModel.setSortOption(SortOption.nameDesc);
                    Navigator.pop(context);
                  },
                ),

                const SizedBox(height: 8),

                // 出生日期分組
                _buildSortGroupTitle('出生日期'),
                _buildSortOption(
                  title: '最新在前',
                  icon: Icons.calendar_today,
                  isSelected: _viewModel.currentSortOption == SortOption.dateNewest,
                  onTap: () async {
                    await _viewModel.setSortOption(SortOption.dateNewest);
                    Navigator.pop(context);
                  },
                ),
                _buildSortOption(
                  title: '最舊在前',
                  icon: Icons.calendar_today,
                  isSelected: _viewModel.currentSortOption == SortOption.dateOldest,
                  onTap: () async {
                    await _viewModel.setSortOption(SortOption.dateOldest);
                    Navigator.pop(context);
                  },
                ),

                const SizedBox(height: 8),

                // 建立時間分組
                _buildSortGroupTitle('建立時間'),
                _buildSortOption(
                  title: '最新建立在前',
                  icon: Icons.access_time,
                  isSelected: _viewModel.currentSortOption == SortOption.createdNewest,
                  onTap: () async {
                    await _viewModel.setSortOption(SortOption.createdNewest);
                    Navigator.pop(context);
                  },
                ),
                _buildSortOption(
                  title: '最舊建立在前',
                  icon: Icons.access_time,
                  isSelected: _viewModel.currentSortOption == SortOption.createdOldest,
                  onTap: () async {
                    await _viewModel.setSortOption(SortOption.createdOldest);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 建立排序分組標題
  Widget _buildSortGroupTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, bottom: 4.0, top: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: AppColors.royalIndigo,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildSortOption({
    required String title,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: isSelected ? Colors.blue : Colors.grey),
      title: Text(title, style: TextStyle(fontWeight: isSelected ? FontWeight.bold : FontWeight.normal)),
      trailing: isSelected ? const Icon(Icons.check, color: Colors.blue) : null,
      onTap: onTap,
      selected: isSelected,
      selectedTileColor: Colors.blue.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 使用 AnimatedBuilder 監聽 ViewModel 的變化
    return AnimatedBuilder(
      animation: _viewModel,
      builder: (context, child) {
        if (_viewModel.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
                _viewModel.isMultiSelectMode
                  ? _viewModel.selectedItems.length == 2
                    ? '已選擇 2 個人 - 點擊下方按鈕選擇星盤類型'
                    : '已選擇 ${_viewModel.selectedItems.length} 個'
                  : '檔案'),
            actions: [
              if (!_viewModel.isMultiSelectMode) ...[
                // 匯入按鈕
                IconButton(
                  icon: const Icon(Icons.file_download, color: Colors.white),
                  tooltip: '從 CSV 匯入',
                  onPressed: _importFromCsv,
                ),
                // 匯出按鈕
                IconButton(
                  icon: const Icon(Icons.file_upload, color: Colors.white),
                  tooltip: '匯出為 CSV',
                  onPressed: _exportToCsv,
                ),
                // 多選模式按鈕
                IconButton(
                  icon: const Icon(Icons.checklist, color: Colors.white),
                  tooltip: '多選模式',
                  onPressed: () {
                    _viewModel.toggleMultiSelectMode();
                  },
                ),
              ] else ...[
                // 全選按鈕
                IconButton(
                  icon: const Icon(Icons.select_all, color: Colors.white),
                  tooltip: '全選',
                  onPressed: () {
                    _viewModel.toggleSelectAll();
                  },
                ),
                // 移除「查看合盤」按鈕，改為在選擇兩個人時自動導航到星盤選擇頁面
                // 複製按鈕
                IconButton(
                  icon: const Icon(Icons.content_copy, color: Colors.white),
                  tooltip: '複製所選項目',
                  onPressed: _viewModel.selectedItems.isEmpty || _viewModel.isCopying
                    ? null
                    : () {
                      _copySelectedItems();
                    },
                ),
                // 刪除按鈕
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  tooltip: '刪除所選項目',
                  onPressed: _viewModel.selectedItems.isEmpty
                    ? null
                    : () {
                      _confirmDeleteSelected();
                    },
                ),
                // 退出多選按鈕
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  tooltip: '退出多選',
                  onPressed: () {
                    _viewModel.toggleMultiSelectMode();
                  },
                ),
          ],
        ],
      ),
          body: _viewModel.birthDataList.isEmpty ? _buildEmptyState() :
                 (_viewModel.searchController.text.isNotEmpty && _viewModel.filteredList.isEmpty) ? _buildNoSearchResultsState() : _buildBirthDataList(),
          floatingActionButton: _viewModel.isMultiSelectMode && _viewModel.selectedItems.length == 2
            ? FloatingActionButton.extended(
                onPressed: () {
                  _navigateToChartSelection();
                },
                tooltip: '選擇星盤類型',
                icon: const Icon(Icons.auto_graph),
                label: const Text('選擇星盤類型'),
                backgroundColor: AppColors.royalIndigo,
              )
            : FloatingActionButton(
                onPressed: () {
                  _showAddBirthDataDialog();
                },
                tooltip: '新增出生資料',
                child: const Icon(Icons.add),
              ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Container(
            width: 140,
            height: 140,
            decoration: BoxDecoration(
              color: AppColors.paleAmber.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.folder_open, size: 70, color: AppColors.solarAmber),
          ),
          const SizedBox(height: 24),
          const Text(
            '尚無出生資料',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: AppColors.textDark),
          ),
          const SizedBox(height: 16),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 40),
            child: Text(
              '點擊右下角按鈕添加出生資料',
              style: TextStyle(fontSize: 16, color: AppColors.textMedium),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 40),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('新增出生資料'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.solarAmber,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            onPressed: () {
              _showAddBirthDataDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNoSearchResultsState() {
    return Column(
      children: [
        // 搜索欄和排序按鈕
        Row(
          children: [
            // 搜索欄
            Expanded(
              child: Container(
                margin: const EdgeInsets.fromLTRB(16.0, 16.0, 8.0, 8.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _viewModel.searchController,
                  decoration: InputDecoration(
                    hintText: '搜索出生資料...',
                    hintStyle: const TextStyle(color: AppColors.textMedium),
                    prefixIcon: const Icon(Icons.search, color: AppColors.royalIndigo),
                    suffixIcon: _viewModel.searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, color: AppColors.royalIndigo),
                            onPressed: () {
                              _viewModel.clearSearch();
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 14.0, horizontal: 16.0),
                  ),
                ),
              ),
            ),
            // 排序按鈕
            if (!_viewModel.isMultiSelectMode)
              Container(
                margin: const EdgeInsets.fromLTRB(0, 16.0, 16.0, 8.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.sort, color: AppColors.royalIndigo),
                  tooltip: '排序',
                  onPressed: _showSortOptionsDialog,
                ),
              ),
          ],
        ),
        // 無搜索結果提示
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColors.softGray,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.search_off, size: 60, color: AppColors.textMedium),
                ),
                const SizedBox(height: 24),
                const Text(
                  '沒有符合的搜索結果',
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: AppColors.textDark),
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    '沒有找到包含 "${_viewModel.searchController.text}" 的出生資料',
                    style: const TextStyle(fontSize: 16, color: AppColors.textMedium),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  icon: const Icon(Icons.clear),
                  label: const Text('清除搜索'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.royalIndigo,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                  onPressed: () {
                    _viewModel.clearSearch();
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBirthDataList() {
    return Column(
      children: [
        // 搜索欄和排序按鈕
        Row(
          children: [
            // 搜索欄
            Expanded(
              child: Container(
                margin: const EdgeInsets.fromLTRB(16.0, 16.0, 8.0, 8.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _viewModel.searchController,
                  decoration: InputDecoration(
                    hintText: '搜索出生資料...',
                    hintStyle: const TextStyle(color: AppColors.textMedium),
                    prefixIcon: const Icon(Icons.search, color: AppColors.royalIndigo),
                    suffixIcon: _viewModel.searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, color: AppColors.royalIndigo),
                            onPressed: () {
                              _viewModel.clearSearch();
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 14.0, horizontal: 16.0),
                  ),
                ),
              ),
            ),
            // 排序按鈕
            if (!_viewModel.isMultiSelectMode)
              Container(
                margin: const EdgeInsets.fromLTRB(0, 16.0, 16.0, 8.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.sort, color: AppColors.royalIndigo),
                  tooltip: '排序',
                  onPressed: _showSortOptionsDialog,
                ),
              ),
          ],
        ),
        // 列表
        Expanded(
          child: RefreshIndicator(
            onRefresh: () => _viewModel.loadBirthData(),
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              itemCount: _viewModel.filteredList.length,
              itemBuilder: (context, index) {
                final data = _viewModel.filteredList[index];
                final isSelected = _viewModel.selectedItems.contains(data.id);

          return StyledCard(
            elevation: 2.0,
            margin: const EdgeInsets.only(bottom: 16.0),
            borderRadius: BorderRadius.circular(16.0),
            color: isSelected ? AppColors.royalIndigo.withOpacity(0.1) : Colors.white,
            isSelected: isSelected,
            onTap: () {
              if (_viewModel.isMultiSelectMode) {
                _viewModel.toggleItemSelection(data.id);
              } else if (widget.isSelectionMode) {
                // 選擇模式：返回選中的出生資料
                Navigator.pop(context, data);
              } else {
                // 直接進入基本星盤
                final chartData = ChartData(
                  chartType: ChartType.natal,
                  primaryPerson: data,
                );

                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ChangeNotifierProvider(
                      create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
                      child: ChartPage(chartData: chartData),
                    ),
                  ),
                );
              }
            },
            onLongPress: () {
              if (!_viewModel.isMultiSelectMode) {
                _viewModel.toggleMultiSelectMode();
                _viewModel.toggleItemSelection(data.id);
              }
            },
            child: Row(
              children: [
                // 左側選擇框或頭像
                _viewModel.isMultiSelectMode
                  ? Checkbox(
                      value: isSelected,
                      onChanged: (bool? value) {
                        _viewModel.toggleItemSelection(data.id);
                      },
                    )
                  : Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.royalIndigo,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.royalIndigo.withOpacity(0.2),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          data.name.isNotEmpty
                            ? data.name.substring(0, 1)
                            : '?',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                const SizedBox(width: 16),
                // 中間內容區域
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        data.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 8.0),
                      Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16, color: AppColors.textMedium),
                          const SizedBox(width: 4),
                          Text(
                            _viewModel.formatDateTime(data.birthDate),
                            style: const TextStyle(color: AppColors.textMedium, fontSize: 14),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4.0),
                      Row(
                        children: [
                          const Icon(Icons.location_on, size: 16, color: AppColors.textMedium),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              data.birthPlace,
                              style: const TextStyle(color: AppColors.textMedium, fontSize: 14),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      if (data.notes != null && data.notes!.isNotEmpty) ...[
                        const SizedBox(height: 4.0),
                        Row(
                          children: [
                            const Icon(Icons.note, size: 16, color: AppColors.textMedium),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                data.notes!,
                                style: const TextStyle(color: AppColors.textMedium, fontSize: 14),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                // 右側操作按鈕
                if (!_viewModel.isMultiSelectMode)
                  IconButton(
                    icon: const Icon(Icons.more_vert, color: AppColors.royalIndigo),
                    onPressed: () {
                      _showOptionsMenu(context, data, index);
                    },
                  ),
              ],
            ),
          );
        },
      ),
    ),
        ),
      ],
    );
  }



  void _showOptionsMenu(BuildContext context, BirthData data, int index) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                // 標題列
                Padding(
                  padding: const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0),
                  child: Row(
                    children: [
                      Text(
                        data.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close, color: AppColors.textMedium),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                // 編輯選項
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.edit, color: AppColors.royalIndigo),
                  ),
                  title: const Text('編輯', style: TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: const Text('修改出生資料的詳細信息'),
                  onTap: () {
                    Navigator.pop(context);
                    _showEditBirthDataDialog(data, index);
                  },
                ),
                // 刪除選項
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.error.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.delete, color: AppColors.error),
                  ),
                  title: const Text('刪除', style: TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: const Text('永久刪除此出生資料'),
                  onTap: () {
                    Navigator.pop(context);
                    _confirmDelete(data.id);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _confirmDelete(String id) {
    // 找到要刪除的項目名稱，用於顯示確認訊息
    final data = _viewModel.filteredList.firstWhere((data) => data.id == id, orElse: () => BirthData(
      id: '',
      name: '',
      birthDate: DateTime.now(),
      birthPlace: '',
      latitude: 0,
      longitude: 0,
    ));

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.delete, color: AppColors.error),
              ),
              const SizedBox(width: 12),
              const Text('確認刪除'),
            ],
          ),
          content: Text(
            '確定要刪除 ${data.name} 的出生資料嗎？此操作無法撤銷。',
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              style: TextButton.styleFrom(foregroundColor: AppColors.textMedium),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: const Text('刪除'),
              onPressed: () async {
                await _viewModel.deleteBirthData(id);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${data.name} 的出生資料已刪除'),
                    backgroundColor: AppColors.royalIndigo,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  // 導航到星盤選擇頁面
  void _navigateToChartSelection() {
    if (_viewModel.selectedItems.length != 2) return;

    // 獲取選中的兩個人
    final selectedPeople = _viewModel.birthDataList
        .where((data) => _viewModel.selectedItems.contains(data.id))
        .toList();

    if (selectedPeople.length != 2) return;

    // 將第一個人設為主要人物，第二個人設為次要人物
    final primaryPerson = selectedPeople[0];
    final secondaryPerson = selectedPeople[1];

    // 導航到星盤選擇頁面，並傳遞兩個人的資料
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartSelectionPage(
          primaryPerson: primaryPerson,
          secondaryPerson: secondaryPerson,
        ),
      ),
    );

    // 退出多選模式
    _viewModel.toggleMultiSelectMode();
  }

  // 確認刪除所選項目
  // 複製選中的項目
  Future<void> _copySelectedItems() async {
    if (_viewModel.selectedItems.isEmpty) return;

    final success = await _viewModel.copySelectedBirthData();

    if (mounted && success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已複製 ${_viewModel.selectedItems.length} 條出生資料到剪貼板'),
          duration: const Duration(seconds: 2),
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('複製失敗'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _confirmDeleteSelected() {
    if (_viewModel.selectedItems.isEmpty) return;

    final selectedCount = _viewModel.selectedItems.length;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.delete, color: AppColors.error),
              ),
              const SizedBox(width: 12),
              const Text('確認刪除'),
            ],
          ),
          content: Text(
            '確定要刪除所選的 $selectedCount 條出生資料嗎？此操作無法撤銷。',
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              style: TextButton.styleFrom(foregroundColor: AppColors.textMedium),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: const Text('刪除'),
              onPressed: () async {
                final deletedCount = await _viewModel.deleteSelectedBirthData();
                Navigator.of(context).pop();

                // 顯示成功訊息
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('已刪除 $deletedCount 條出生資料'),
                    backgroundColor: AppColors.royalIndigo,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  void _showAddBirthDataDialog() async {
    // 使用 BirthDataFormPage 頁面來新增出生資料
    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => const BirthDataFormPage(),
      ),
    );

    // 如果用戶返回了出生資料，則添加到列表中
    if (result != null) {
      await _viewModel.addBirthData(result);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('出生資料已儲存')),
        );
      }
    }
  }

  void _showEditBirthDataDialog(BirthData data, int displayIndex) async {
    // 使用 BirthDataFormPage 頁面來編輯出生資料
    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => BirthDataFormPage(initialData: data),
      ),
    );

    // 如果用戶返回了更新後的出生資料，則更新列表
    if (result != null) {
      await _viewModel.updateBirthData(data.id, result);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('出生資料已更新')),
        );
      }
    }
  }

  // 匯出出生資料到 CSV 文件
  Future<void> _exportToCsv() async {
    if (_viewModel.birthDataList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有可匯出的出生資料')),
      );
      return;
    }

    try {
      // 顯示加載指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在匯出資料...'),
              ],
            ),
          );
        },
      );

      // 匯出數據
      final filePath = await _viewModel.exportToCsv();

      // 關閉加載對話框
      if (mounted) Navigator.of(context).pop();

      if (filePath != null && filePath.isNotEmpty) {
        // 顯示分享選項
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('匯出成功'),
                content: const Text('出生資料已成功匯出為 CSV 文件。您想要分享這個文件嗎？'),
                actions: [
                  TextButton(
                    child: const Text('取消'),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                  TextButton(
                    child: const Text('分享'),
                    onPressed: () async {
                      Navigator.of(context).pop();
                      await CsvHelper.shareCsvFile(filePath);
                    },
                  ),
                ],
              );
            },
          );
        }
      }
    } catch (e) {
      // 關閉加載對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('匯出資料時出錯: $e')),
        );
      }
      logger.e('匯出資料時出錯: $e');
    }
  }

  // 從 CSV 文件匯入出生資料
  Future<void> _importFromCsv() async {
    try {
      // 匯入數據
      final importedData = await _viewModel.importFromCsv();

      if (importedData.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('沒有匯入任何資料')),
          );
        }
        return;
      }

      // 確認匯入
      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('確認匯入'),
              content: Text('找到 ${importedData.length} 條出生資料。您想要如何匯入？'),
              actions: [
                TextButton(
                  child: const Text('取消'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('替換現有資料'),
                  onPressed: () async {
                    await _viewModel.replaceExistingData(importedData);
                    if (mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('已匯入 ${importedData.length} 條出生資料並替換現有資料')),
                      );
                    }
                  },
                ),
                TextButton(
                  child: const Text('合併到現有資料'),
                  onPressed: () async {
                    final newDataCount = await _viewModel.mergeWithExistingData(importedData);
                    if (mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('已合併 $newDataCount 條新出生資料')),
                      );
                    }
                  },
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('匯入資料時出錯: $e')),
        );
      }
      logger.e('匯入資料時出錯: $e');
    }
  }
}
