import 'dart:convert';

import 'package:astreal/models/birth_data.dart';
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/models/chart_settings.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/models/copy_options.dart';
import 'package:astreal/models/firdaria_data.dart';
import 'package:astreal/services/chart_service.dart';
import 'package:astreal/services/firdaria_service.dart';
import 'package:astreal/utils/chart_email_sender.dart';
import 'package:astreal/utils/chart_pdf_generator.dart';
import 'package:astreal/utils/logger_utils.dart';
import 'package:astreal/viewmodels/chart_interpretation_viewmodel.dart';
import 'package:astreal/viewmodels/recent_charts_viewmodel.dart';
import 'package:clipboard/clipboard.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/ascension_table.dart';
import '../constants/astrology_constants.dart';
import '../models/aspect_info.dart';
import '../models/planet_position.dart';
import '../models/term_ruler_progression_result.dart';
import '../models/term_ruler_timeline_result.dart';
import '../services/astrology_service.dart';
import '../utils/astrology_calculator.dart' as astro;

// 用于管理星盘数据和相关操作。
class ChartViewModel extends ChangeNotifier {
  // 使用 ChartService 處理星盤相關的計算。
  final ChartService _chartService = ChartService();

  // 使用 AstrologyService 處理占星相關的計算。
  final AstrologyService _astrologyService = AstrologyService();

  // 用於追蹤異步操作的加載狀態。
  bool _isLoading = true;

  // 用於追蹤電子郵件發送狀態。
  bool _isSendingEmail = false;

  // 用於追蹤 PDF 生成狀態。
  bool _isGeneratingPdf = false;

  // 用於追蹤複製操作狀態。
  bool _isCopying = false;

  // 用於控制個人資訊是否展開顯示。
  bool _isPersonInfoExpanded = true;

  // 用於顯示位置資訊的狀態。
  String _locationStatus = '';

  // 用於追蹤星盤變更的唯一標識符
  int _chartUpdateCounter = 0;

  // Data（數據）
  // 儲存星盤的主要數據。
  late ChartData _chartData;

  // 儲存星盤的類型。
  late ChartType _chartType;

  // 儲存主要人物的出生數據。
  late BirthData _primaryPerson;

  // 儲存次要人物的出生數據（如果有的話）。
  BirthData? _secondaryPerson;

  // 儲存特定日期，用於某些星盤類型。
  DateTime? _specificDate;

  // 儲存本命行星的位置。
  final List<PlanetPosition> _natalPlanets = [];

  // 法達盤數據
  List<FirdariaData>? _firdariaData;

  // 選中的法達盤週期索引
  int? _selectedFirdariaPeriodIndex;

  // 是否為白天出生
  bool _isDaytimeBirth = true;

  // 儲存緯度。
  double? _latitude;

  // 儲存經度。
  double? _longitude;

  // 用於處理 AI 星盤解讀的 ViewModel。
  late ChartInterpretationViewModel _interpretationViewModel;

  // 星盤設定
  ChartSettings? _chartSettings;

  // 返回加載狀態。
  bool get isLoading => _isLoading;

  // 返回電子郵件發送狀態。
  bool get isSendingEmail => _isSendingEmail;

  // 返回 PDF 生成狀態。
  bool get isGeneratingPdf => _isGeneratingPdf;

  // 返回複製狀態。
  bool get isCopying => _isCopying;

  // 返回個人資訊是否展開的狀態。
  bool get isPersonInfoExpanded => _isPersonInfoExpanded;

  // 返回位置資訊的狀態。
  String get locationStatus => _locationStatus;

  // 返回星盤更新計數器
  int get chartUpdateCounter => _chartUpdateCounter;

  // 返回星盤數據。
  ChartData get chartData => _chartData;

  // 返回星盤類型。
  ChartType get chartType => _chartType;

  // 返回主要人物的出生數據。
  BirthData get primaryPerson => _primaryPerson;

  // 返回次要人物的出生數據。
  BirthData? get secondaryPerson => _secondaryPerson;

  // 返回特定日期。
  DateTime? get specificDate => _specificDate;

  // 返回本命行星的位置。
  List<PlanetPosition> get natalPlanets => _natalPlanets;

  // 返回緯度。
  double? get latitude => _latitude;

  // 返回經度。
  double? get longitude => _longitude;

  // 返回 AI 解讀 ViewModel。
  ChartInterpretationViewModel get interpretationViewModel =>
      _interpretationViewModel;

  // 返回星盤設定
  ChartSettings? get chartSettings => _chartSettings;

  // 返回法達盤數據
  List<FirdariaData>? get firdariaData => _firdariaData;

  // 設置法達盤數據
  set firdariaData(List<FirdariaData>? data) {
    _firdariaData = data;
    notifyListeners();
  }

  // 返回選中的法達盤週期索引
  int? get selectedFirdariaPeriodIndex => _selectedFirdariaPeriodIndex;

  // 設置選中的法達盤週期索引
  set selectedFirdariaPeriodIndex(int? index) {
    _selectedFirdariaPeriodIndex = index;
    notifyListeners();
  }

  // 返回是否為白天出生
  bool get isDaytimeBirth => _isDaytimeBirth;

  // 設置是否為白天出生
  set isDaytimeBirth(bool value) {
    _isDaytimeBirth = value;
    notifyListeners();
  }

  // Constructor（構造函數）
  // 默認構造函數
  ChartViewModel() {
    // 創建一個默認的 ChartData 對象
    _chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: BirthData(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: '',
        birthDate: DateTime.now(),
        birthPlace: '',
        latitude: 0,
        longitude: 0,
      ),
    );
    _chartType = _chartData.chartType;
    _primaryPerson = _chartData.primaryPerson;
    _interpretationViewModel = ChartInterpretationViewModel();
  }

  // 構造函數，接收初始星盤數據。
  ChartViewModel.withChartData(
      {required ChartData initialChartData, BuildContext? context}) {
    _chartData = initialChartData;
    _chartType = initialChartData.chartType;
    _primaryPerson = initialChartData.primaryPerson;
    _secondaryPerson = initialChartData.secondaryPerson;
    _specificDate = initialChartData.specificDate;
    _interpretationViewModel = ChartInterpretationViewModel();

    // 計算星盤數據
    calculateChart();

    // 記錄到最近使用的星盤
    if (context != null) {
      _addToRecentCharts(context);
    }
  }

  String getChartTitle() {
    final primaryName = primaryPerson.name;
    final secondaryName = secondaryPerson?.name;

    switch (chartType) {
      case ChartType.mundane:
      case ChartType.equinoxSolstice:
        return primaryName;
      case ChartType.natal:
        return '$primaryName 的 ${chartType.name}';
      default:
        if (chartType.requiresTwoPersons && secondaryPerson != null) {
          return '$primaryName 與 $secondaryName 的${chartType.name}';
        } else {
          return '$primaryName的${chartType.name}';
        }
    }
  }

  /// 將當前星盤添加到最近使用記錄
  void _addToRecentCharts(BuildContext context) {
    try {
      final recentChartsViewModel =
          Provider.of<RecentChartsViewModel>(context, listen: false);
      recentChartsViewModel.addOrUpdateRecentChart(_chartData);
      logger.d('已將星盤添加到最近使用記錄');
    } catch (e) {
      logger.e('添加到最近使用記錄時出錯: $e');
    }
  }

  /// 設置主要人物的出生數據
  void setPrimaryPerson(BirthData birthData) {
    _primaryPerson = birthData;
    // 創建新的 ChartData 對象
    _chartData = ChartData(
      chartType: _chartType,
      primaryPerson: birthData,
      secondaryPerson: _secondaryPerson,
      specificDate: _specificDate,
      planets: _chartData.planets,
      houses: _chartData.houses,
      aspects: _chartData.aspects,
      arabicPoints: _chartData.arabicPoints,
    );
    _latitude = birthData.latitude;
    _longitude = birthData.longitude;

    // 重新計算星盤數據
    calculateChart();
  }

  /// 設置次要人物的出生數據
  void setSecondaryPerson(BirthData? birthData) {
    _secondaryPerson = birthData;
    // 創建新的 ChartData 對象
    _chartData = ChartData(
      chartType: _chartType,
      primaryPerson: _primaryPerson,
      secondaryPerson: birthData,
      specificDate: _specificDate,
      planets: _chartData.planets,
      houses: _chartData.houses,
      aspects: _chartData.aspects,
      arabicPoints: _chartData.arabicPoints,
    );

    // 重新計算星盤數據
    calculateChart();
  }

  /// 設置特定日期（用於推運盤和返照盤）
  void setSpecificDate(DateTime date) {
    logger.i('設置特定日期: $date');
    _specificDate = date;
    // 創建新的 ChartData 對象
    _chartData = ChartData(
      chartType: _chartType,
      primaryPerson: _primaryPerson,
      secondaryPerson: _secondaryPerson,
      specificDate: date,
      planets: _chartData.planets,
      houses: _chartData.houses,
      aspects: _chartData.aspects,
      arabicPoints: _chartData.arabicPoints,
    );

    // 重新計算星盤數據
    calculateChart();
  }

  /// 計算法達盤數據
  Future<void> calculateFirdaria() async {
    logger.i('開始計算法達盤數據');
    setLoading(true);

    try {
      // 判斷是否為白天出生
      _isDaytimeBirth = _isDaytimeBirth;

      // 使用 FirdariaService 計算法達盤數據
      final firdariaService = FirdariaService();
      _firdariaData = await firdariaService.calculateFirdaria(
        _primaryPerson,
        isDaytime: _isDaytimeBirth,
        currentDate: DateTime.now(),
      );

      logger.i('法達盤數據計算完成，共 ${_firdariaData?.length} 個週期');

      // 如果有法達盤數據，選擇當前週期
      if (_firdariaData != null && _firdariaData!.isNotEmpty) {
        // 找到當前週期的索引
        final currentIndex =
            _firdariaData!.indexWhere((period) => period.isCurrent);
        if (currentIndex >= 0) {
          _selectedFirdariaPeriodIndex = currentIndex;
        }
      }
    } catch (e, stackTrace) {
      logger.e('計算法達盤時出錯: $e');
      logger.e('堆疊追蹤: $stackTrace');
    } finally {
      setLoading(false);
      notifyListeners(); // 確保 UI 更新
    }
  }

  /// 計算星盤數據
  ///
  /// 此方法執行以下操作：
  /// 1. 設置加載狀態為「正在加載」
  /// 2. 加載用戶的星盤設定（包括行星可見性設定）
  /// 3. 使用 ChartService 計算行星位置、宮位和相位
  /// 4. 計算行星之間的互容接納關係
  /// 5. 對特定星盤類型進行額外計算（如推運盤和返照盤）
  /// 6. 設置加載狀態為「完成」
  Future<void> calculateChart() async {
    logger.i('開始計算${_chartType.name}星盤數據');
    setLoading(true);

    try {
      // 加載用戶的星盤設定
      _chartSettings = await ChartSettings.loadFromPrefs();
      logger.d('已加載用戶星盤設定');

      // 如果是本命盤，先儲存行星位置供其他星盤類型使用
      bool isNatalChart = _chartType == ChartType.natal;

      // 確保行星、宮位和相位列表已初始化
      _chartData.planets ??= [];
      _chartData.aspects ??= [];

      // 使用 ChartService 計算行星位置、宮位和相位，並傳遞行星可見性設定和相位容許度設定
      _chartData = await _chartService.calculatePlanetPositionsForChart(
        _chartData,
        planetVisibility: _chartSettings!.planetVisibility,
        aspectOrbs: _chartSettings!.aspectOrbs,
      );
      logger.d('基本星盤數據計算完成');

      // 如果是本命盤，儲存行星位置供其他星盤類型使用
      if (isNatalChart &&
          _chartData.planets != null &&
          _chartData.planets!.isNotEmpty) {
        _natalPlanets.clear();
        _natalPlanets.addAll(_chartData.planets!);
        logger.d('已儲存本命盤行星位置，共${_natalPlanets.length}個行星');
      }

      // 計算行星之間的互容接納關係
      if (_chartData.planets != null && _chartData.planets!.isNotEmpty) {
        logger.d('計算行星互容接納關係');
        final receptions =
            _chartService.calculateReceptions(_chartData.planets!);

        // 將互容接納關係添加到相位列表中
        _chartData.aspects ??= [];

        // 將互容接納關係添加到相位列表中
        _chartData.aspects!.addAll(receptions);
        logger.d('行星互容接納關係計算完成，共${receptions.length}個接納關係');
      }

      // 處理需要額外計算的特定星盤類型
      // 對於推運盤和返照盤，需要計算本命盤與推運行星之間的相位
      final progressionAndReturnTypes = [
        ChartType.secondaryProgression,
        ChartType.tertiaryProgression,
        ChartType.solarArcDirection,
        ChartType.solarReturn,
        ChartType.lunarReturn,
        ChartType.transit,
        ChartType.synastry,
        ChartType.composite,
        ChartType.davison,
        ChartType.marks,
      ];

      if (progressionAndReturnTypes.contains(_chartType)) {
        // 確保有本命盤行星數據
        if (_natalPlanets.isEmpty) {
          logger.w('無法計算${_chartType.name}的雙圈盤相位：缺少本命盤行星數據');
        } else if (_chartData.planets == null || _chartData.planets!.isEmpty) {
          logger.w('無法計算${_chartType.name}的雙圈盤相位：缺少第二個圈的行星數據');
        } else {
          logger.d('計算${_chartType.name}的雙圈盤相位');
          _chartData.aspects =
              astro.AstrologyCalculator.calculateDualChartAspects(
            _natalPlanets, // 本命盤行星
            _chartData.planets!, // 推運或返照盤行星
            aspectOrbs: _chartSettings!.aspectOrbs, // 傳遞相位容許度設定
          );
          logger.d('雙圈盤相位計算完成，共${_chartData.aspects!.length}個相位');
        }
      }

      logger.i('${_chartType.name}星盤數據計算完成');

      // 增加星盤更新計數器
      _chartUpdateCounter++;
      logger.d('星盤更新計數器增加到: $_chartUpdateCounter');
    } catch (e, stackTrace) {
      logger.e('計算星盤時出錯: $e');
      logger.e('堆疊追蹤: $stackTrace');
    } finally {
      setLoading(false);
      notifyListeners(); // 確保 UI 更新
    }
  }

  // Setters（Setter 方法）
  // 設置加載狀態並通知監聽器。
  void setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  // 設置電子郵件發送狀態並通知監聽器。
  void setSendingEmail(bool sending) {
    _isSendingEmail = sending;
    notifyListeners();
  }

  // 設置 PDF 生成狀態並通知監聽器。
  void setGeneratingPdf(bool generating) {
    _isGeneratingPdf = generating;
    notifyListeners();
  }

  // 設置複製狀態並通知監聽器。
  void setCopying(bool copying) {
    _isCopying = copying;
    notifyListeners();
  }

  // 切換個人資訊的展開/摺疊狀態並通知監聽器。
  void togglePersonInfoExpanded() {
    _isPersonInfoExpanded = !_isPersonInfoExpanded;
    notifyListeners();
  }

  // 設置星盤類型並重新計算星盤。
  Future<void> setChartType(ChartType type, {BuildContext? context}) async {
    if (type != _chartType) {
      logger.i('切換星盤類型：從 ${_chartType.name} 到 ${type.name}');

      // 如果新的星盤類型需要兩個人，但目前沒有第二個人的資料
      if (type.requiresTwoPersons &&
          _secondaryPerson == null &&
          context != null) {
        // 顯示出生資料選擇對話框
        final selectedPerson = await showPersonSelectionDialog(context);

        // 如果用戶取消了選擇，則不更改星盤類型
        if (selectedPerson == null) {
          logger.w('用戶取消了選擇第二個人，不更改星盤類型');
          return;
        }

        // 設置第二個人的資料
        _secondaryPerson = selectedPerson;
        _chartData.secondaryPerson = selectedPerson;
        logger.d('已設置第二個人的資料：${selectedPerson.name}');
      }

      // 更新星盤類型
      _chartType = type;
      _chartData.chartType = type;

      // 清空現有的行星、宮位和相位數據，確保完全重新計算
      _chartData.planets = [];
      _chartData.houses = null;
      _chartData.aspects = [];
      _chartData.arabicPoints = [];

      // 設置加載狀態並通知 UI 更新
      setLoading(true);
      notifyListeners();

      // 如果是法達盤，計算法達盤數據
      if (type == ChartType.firdaria) {
        await calculateChart(); // 先計算基本星盤數據
        await calculateFirdaria(); // 再計算法達盤數據
      } else {
        // 重新計算星盤數據
        await calculateChart();
      }

      // 記錄到最近使用的星盤
      if (context != null) {
        _addToRecentCharts(context);
      }

      // 確保 UI 更新
      notifyListeners();
      logger.i('星盤類型切換完成：${type.name}');
    }
  }

  /// 使用新的 ChartData 更新星盤
  Future<void> updateChartData(ChartData newChartData,
      {BuildContext? context}) async {
    logger.i('使用新的 ChartData 更新星盤: ${newChartData.chartType.name}');

    // 更新星盤數據
    _chartData = newChartData;

    // 更新相關屬性
    _chartType = newChartData.chartType;
    _primaryPerson = newChartData.primaryPerson;
    _secondaryPerson = newChartData.secondaryPerson;
    _specificDate = newChartData.specificDate;

    // 清空現有的行星、宮位和相位數據，確保完全重新計算
    _chartData.planets = [];
    _chartData.houses = null;
    _chartData.aspects = [];
    _chartData.arabicPoints = [];
    _natalPlanets.clear();

    // 設置加載狀態
    setLoading(true);
    notifyListeners();

    // 重新計算星盤數據
    await calculateChart();

    // 記錄到最近使用的星盤
    if (context != null) {
      _addToRecentCharts(context);
    }

    // 確保 UI 更新
    notifyListeners();
    logger.i('星盤數據更新完成: ${_chartType.name}');
  }

  /// 顯示出生資料選擇對話框
  Future<BirthData?> showPersonSelectionDialog(BuildContext context) async {
    // 從 SharedPreferences 加載所有出生資料
    final prefs = await SharedPreferences.getInstance();
    final String? birthDataJson = prefs.getString('birthDataList');

    if (birthDataJson == null) {
      // 如果沒有儲存的出生資料，顯示提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('沒有可用的出生資料，請先新增出生資料')),
        );
      }
      return null;
    }

    // 解析出生資料
    final List<dynamic> decodedData = jsonDecode(birthDataJson);
    final List<BirthData> birthDataList = decodedData
        .map((item) => BirthData.fromJson(item))
        .where((data) => data.id != _primaryPerson.id) // 排除主要人物
        .toList();

    if (birthDataList.isEmpty) {
      // 如果沒有其他出生資料，顯示提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('沒有其他出生資料，請先新增出生資料')),
        );
      }
      return null;
    }

    // 顯示選擇對話框
    if (!context.mounted) return null;

    return showDialog<BirthData>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('選擇第二個人的出生資料'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: birthDataList.length,
              itemBuilder: (context, index) {
                final data = birthDataList[index];
                return ListTile(
                  title: Text(data.name),
                  subtitle: Text(
                    '${data.birthDate.year}/${data.birthDate.month}/${data.birthDate.day} ${data.birthDate.hour}:${data.birthDate.minute.toString().padLeft(2, '0')}\n${data.birthPlace}',
                  ),
                  onTap: () {
                    Navigator.of(context).pop(data);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  // 格式化角度為度分秒格式。
  String formatDegree(double degree) {
    final int deg = degree.floor();
    final double minDouble = (degree - deg) * 60;
    final int min = minDouble.floor();
    // final int sec = ((minDouble - min) * 60).round();

    // 確保分和秒都是兩位數字
    final String minStr = min.toString().padLeft(2, '0');
    // final String secStr = sec.toString().padLeft(2, '0');

    return '$deg°$minStr\'';
  }

  // 格式化日期時間為指定格式。
  String formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} '
        '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 根據經度獲取星座。
  String getZodiacSign(double longitude) {
    final int signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }

  // 獲取當前宮位制設定
  Future<String> getCurrentHouseSystem() async {
    try {
      final settings = await ChartSettings.loadFromPrefs();
      return settings.houseSystem;
    } catch (e) {
      logger.e('获取宫位制设置失败: $e');
      return 'Placidus'; // 默认使用 Placidus 系统
    }
  }

  /// 生成包含星盤資訊的文本，用於複製到剪貼簿。
  /// 包含行星位置、宮主星、日夜區分、元素統計等信息
  ///
  /// [options] 複製選項，用於控制要包含哪些信息
  Future<String> generateChartInfoText({CopyOptions? options}) async {
    // 如果沒有提供選項，使用默認選項（全選）
    final opts = options ?? CopyOptions.all();
    final StringBuffer text = StringBuffer();
    final String houseSystem = await getCurrentHouseSystem();

    // 用於美化格式的輔助函數
    String sectionTitle(String title) {
      if (opts.usePrettyFormat) {
        return '\n${'=' * 15}\n$title\n${'=' * 15}';
      } else {
        return '\n【$title】';
      }
    }

    String subSectionTitle(String title) {
      if (opts.usePrettyFormat) {
        return '\n${'-' * 15}\n$title\n${'-' * 15}';
      } else {
        return '$title:';
      }
    }

    // Add title（添加標題）
    text.writeln(sectionTitle('星盤類型'));
    text.writeln(_chartType.name);
    if (chartType == ChartType.equinoxSolstice) {
      text.writeln(_chartData.primaryPerson.name);
    }
    if (chartType == ChartType.firdaria) {
      final currentPeriod = firdariaData!.firstWhere(
        (period) => period.isCurrent,
        orElse: () => firdariaData!.first,
      );
      final currentSubPeriod = currentPeriod.subPeriods.firstWhere(
        (subPeriod) => subPeriod.isCurrent,
        orElse: () => currentPeriod.subPeriods.first,
      );
      text.writeln(
          "主星: ${currentPeriod.majorPlanetName}\n次星: ${currentSubPeriod.subPlanetName}");
    }
    if (_chartType == ChartType.horary) {
      text.writeln(sectionTitle('卜卦問題'));
      text.writeln(_chartData.primaryPerson.notes);
    }

    text.writeln(sectionTitle('宮位制'));
    text.writeln(houseSystem);

    // 判斷是白天還是夜晚出生
    bool? isDaytime;
    if (_chartData.planets != null) {
      // 尋找太陽的位置
      final sun = _chartData.planets!.firstWhere(
        (planet) => planet.id == AstrologyConstants.SUN,
        orElse: () => PlanetPosition(
          id: -1,
          name: '',
          symbol: '',
          longitude: 0,
          latitude: 0,
          distance: 0,
          longitudeSpeed: 0,
          latitudeSpeed: 0,
          distanceSpeed: 0,
          sign: '',
          house: 0,
          dignity: PlanetDignity.normal,
          solarCondition: SolarCondition.free,
          isDaytime: false,
          houseType: HouseType.angular,
          isPlanetDiurnal: false,
          isSignMasculine: false,
          sectStatus: SectStatus.outOfSect,
        ),
      );

      if (sun.id != -1) {
        // 白天出生：太陽在地平線上方（第7宮到第12宮之間）
        // 夜晚出生：太陽在地平線下方（第1宮到第6宮之間）
        isDaytime = sun.house > 6;
        text.writeln(sectionTitle('日夜區分'));
        text.writeln(isDaytime ? '日生盤' : '夜生盤');
      }
    }

    // Add basic info（添加基本資訊）
    if (opts.includeBasicInfo) {
      text.writeln(sectionTitle('基本資料'));
      text.writeln('姓名: ${_primaryPerson.name}');
      text.writeln('出生日期: ${formatDateTime(_primaryPerson.birthDate)}');
      text.writeln('出生地點: ${_primaryPerson.birthPlace}');
      // If specific date is required for chart type, show date info（如果星盤類型需要特定日期，則顯示日期資訊）
      if (_chartType != ChartType.transit &&
          _chartType.requiresSpecificDate &&
          _specificDate != null) {
        text.writeln('參考日期: ${formatDateTime(_specificDate!)}');
      }

      // If it's a transit chart, show transit date（如果是 transit 星盤，顯示 transit 日期）
      if (_chartType == ChartType.transit && _specificDate != null) {
        text.writeln('天象日期: ${formatDateTime(_specificDate!)}');
      }

      // If it's a synastry chart, show second person's info（如果是合盤，顯示第二個人的資訊）
      if (_chartType.requiresTwoPersons && _secondaryPerson != null) {
        text.writeln(subSectionTitle('次要人物資料'));
        text.writeln('姓名: ${_secondaryPerson!.name}');
        text.writeln('出生日期: ${formatDateTime(_secondaryPerson!.birthDate)}');
        text.writeln('出生地點: ${_secondaryPerson!.birthPlace}');
      }
    }

    if (_chartData.planets != null) {
      final sun = _chartData.planets!.firstWhere(
        (planet) => planet.id == AstrologyConstants.SUN,
        orElse: () => PlanetPosition(
          id: -1,
          name: '',
          symbol: '',
          longitude: 0,
          latitude: 0,
          distance: 0,
          longitudeSpeed: 0,
          latitudeSpeed: 0,
          distanceSpeed: 0,
          sign: '',
          house: 0,
          dignity: PlanetDignity.normal,
          solarCondition: SolarCondition.free,
          isDaytime: false,
          houseType: HouseType.angular,
          isPlanetDiurnal: false,
          isSignMasculine: false,
          sectStatus: SectStatus.outOfSect,
        ),
      );
      if (sun.id != -1) {
        isDaytime = sun.house > 6;
      }
    }

    // Add planet positions（添加行星位置）
    if (opts.includePlanetPositions && _chartData.planets != null) {
      text.writeln(sectionTitle('行星'));

      // 預先計算宮主星信息
      Map<int, List<int>> planetToHousesRuled = {};
      if (_chartData.houses != null) {
        // 象限制宮主星
        for (int i = 1; i <= 12; i++) {
          final cuspLongitude = _chartData.houses!.cusps[i];
          final signIndex = (cuspLongitude / 30).floor() % 12;
          final planetId = _getPlanetIdBySignIndex(signIndex);

          if (planetId != -1) {
            if (!planetToHousesRuled.containsKey(planetId)) {
              planetToHousesRuled[planetId] = [];
            }
            planetToHousesRuled[planetId]!.add(i);
          }
        }
      }

      for (final planet in _chartData.planets!) {
        // 跳過阿拉伯點，它們將在特殊點部分顯示
        if (planet.id >= 100) continue;

        final signDegree = planet.longitude % 30;

        // 基本位置信息
        StringBuffer planetInfo = StringBuffer();
        planetInfo.write(
            '${planet.name}: ${planet.sign} ${formatDegree(signDegree)}, ${planet.getHouseText()}');

        // 添加逆行信息
        if (planet.longitudeSpeed < 0) {
          planetInfo.write(' (逆行)');
        }

        // 添加尊貴力量信息
        if (planet.dignity != PlanetDignity.normal) {
          planetInfo.write(' (${planet.getDignityText()})');
        }

        // 添加宮主星信息
        if (planetToHousesRuled.containsKey(planet.id) &&
            planetToHousesRuled[planet.id]!.isNotEmpty) {
          final housesRuled = planetToHousesRuled[planet.id]!;
          final housesText = housesRuled.map((h) => '是第$h宮').join('、');
          planetInfo.write(' $housesText主星');
        }

        // 添加日夜區分信息
        if (isDaytime != null && planet.id <= 6) {
          // 只為主要行星添加日夜區分信息
          planetInfo.write(' ${planet.getSectStatusText()}');
        }

        text.writeln(planetInfo.toString());
      }
    }

    // Add planet dignities (行星尊貴力量)
    if (opts.includePlanetDignities && _chartData.planets != null) {
      text.writeln(sectionTitle('行星尊貴力量'));
      for (final planet in _chartData.planets!) {
        // 只顯示主要行星（太陽到土星）的尊貴力量
        if (planet.id <= 6 && planet.dignity != PlanetDignity.normal) {
          text.writeln(
              '${planet.name}: ${planet.getDignityText()} (在${planet.sign})');
        }
      }
    }

    // Add planet sect status (行星日夜區分)
    if (opts.includePlanetSectStatus &&
        _chartData.planets != null &&
        isDaytime != null) {
      text.writeln(sectionTitle('行星日夜區分'));
      for (final planet in _chartData.planets!) {
        // 只顯示主要行星（太陽到土星）的日夜區分
        if (planet.id <= 6) {
          text.writeln('${planet.name}: ${planet.getSectStatusText()}');
        }
      }
    }

    // Add house rulers (宮主星)
    if (opts.includeHouseRulers &&
        _chartData.planets != null &&
        _chartData.houses != null) {
      text.writeln(sectionTitle('宮主星'));

      // 象限制宮主星
      text.writeln(subSectionTitle('象限制'));
      for (int i = 1; i <= 12; i++) {
        final cuspLongitude = _chartData.houses!.cusps[i];
        final houseRuler = _getHouseRuler(cuspLongitude);
        text.writeln('  第$i宮: $houseRuler');
      }

      // 整宮制宮主星
      text.writeln(subSectionTitle('整宮制'));
      if (_chartData.houses != null) {
        // 獲取上升星座的索引
        final ascendantLongitude = _chartData.houses!.cusps[1];
        final ascendantSignIndex = (ascendantLongitude / 30).floor() % 12;

        for (int i = 0; i < 12; i++) {
          // 計算整宮制中第 i+1 宮對應的星座索引
          final signIndex = (ascendantSignIndex + i) % 12;
          final sign = AstrologyConstants.ZODIAC_SIGNS[signIndex];
          final houseRuler = _getHouseRulerBySignIndex(signIndex);
          text.writeln('  第${i + 1}宮(${sign}): $houseRuler');
        }
      }
    }

    // Add house positions（添加宮位位置）
    if (opts.includeHousePositions && _chartData.houses != null) {
      text.writeln(sectionTitle('宮位'));
      text.writeln('宮位制: $houseSystem');
      for (int i = 1; i <= 12; i++) {
        final houseAngle = _chartData.houses!.cusps[i];
        final sign = getZodiacSign(houseAngle);
        final signDegree = houseAngle % 30;
        text.writeln('第$i宮: $sign ${formatDegree(signDegree)}');
      }
    }

    // Add aspects（添加相位）
    if (opts.includeAspects &&
        _chartData.aspects != null &&
        _chartData.aspects!.isNotEmpty) {
      // 過濾出普通相位（非互容接納）
      final normalAspects = _chartData.aspects!
          .where((aspect) => aspect.receptionType == ReceptionType.none)
          .toList();

      if (normalAspects.isNotEmpty) {
        text.writeln(sectionTitle('相位'));
        for (final aspect in normalAspects) {
          // 添加入相或出相的信息
          String directionText =
              aspect.direction != null ? ' ${aspect.getDirectionText()}' : '';
          text.writeln(
            '${aspect.planet1.name} ${aspect.shortZh} ${aspect.planet2.name} (容許度: ${aspect.orb.toStringAsFixed(2)}°$directionText)',
          );
        }
      }

      // 添加互容接納關係
      if (opts.includeReceptions) {
        final receptions = _chartData.aspects!
            .where((aspect) => aspect.receptionType != ReceptionType.none)
            .toList();

        if (receptions.isNotEmpty) {
          text.writeln(sectionTitle('互容接納'));
          for (final reception in receptions) {
            String receptionTypeText = '';
            switch (reception.receptionType) {
              case ReceptionType.reception:
                receptionTypeText = '互容';
                break;
              case ReceptionType.acceptance:
                receptionTypeText = '接納';
                break;
              case ReceptionType.mutualReception:
                receptionTypeText = '互相互容';
                break;
              case ReceptionType.mutualAcceptance:
                receptionTypeText = '互相接納';
                break;
              default:
                receptionTypeText = '';
            }

            if (receptionTypeText.isNotEmpty) {
              text.writeln(
                '${reception.planet1.name} $receptionTypeText ${reception.planet2.name}',
              );
            }
          }
        }
      }
    }

    // Add element statistics (元素統計)
    if (opts.includeElementStats && _chartData.planets != null) {
      // 計算元素分佈（火土風水）
      final elementStats = _calculateElementStats(_chartData.planets!);
      // 計算品質分佈（啟動、固定、變動）
      final modalityStats = _calculateModalityStats(_chartData.planets!);
      // 計算陰陽分佈
      final polarityStats = _calculatePolarityStats(_chartData.planets!);

      text.writeln(sectionTitle('統計'));

      // 元素分佈（火土風水）
      text.writeln(subSectionTitle('四大元素'));
      text.writeln('  火: ${elementStats['火'] ?? 0}');
      text.writeln('  土: ${elementStats['土'] ?? 0}');
      text.writeln('  風: ${elementStats['風'] ?? 0}');
      text.writeln('  水: ${elementStats['水'] ?? 0}');

      // 品質分佈（啟動、固定、變動）
      text.writeln(subSectionTitle('三大性質'));
      text.writeln('  啟動: ${modalityStats['啟動'] ?? 0}');
      text.writeln('  固定: ${modalityStats['固定'] ?? 0}');
      text.writeln('  變動: ${modalityStats['變動'] ?? 0}');

      // 陰陽分佈
      text.writeln(subSectionTitle('陰陽分佈'));
      text.writeln('  陽性: ${polarityStats['陽性'] ?? 0}');
      text.writeln('  陰性: ${polarityStats['陰性'] ?? 0}');

      // 元素行星詳細
      text.writeln(sectionTitle('元素行星詳細'));

      // 元素分組詳細
      final elementPlanets = _getPlanetsByElement(_chartData.planets!);
      text.writeln('火元素: ${_formatPlanetList(elementPlanets['火'] ?? [])}');
      text.writeln('土元素: ${_formatPlanetList(elementPlanets['土'] ?? [])}');
      text.writeln('風元素: ${_formatPlanetList(elementPlanets['風'] ?? [])}');
      text.writeln('水元素: ${_formatPlanetList(elementPlanets['水'] ?? [])}');
    }

    // Add Arabic Points (特殊點/阿拉伯點)
    if (opts.includeArabicPoints &&
        _chartData.arabicPoints != null &&
        _chartData.arabicPoints!.isNotEmpty) {
      text.writeln(sectionTitle('特殊點'));
      for (final point in _chartData.arabicPoints!) {
        // 只顯示前幾個重要的阿拉伯點
        if (point.id >= AstrologyConstants.FORTUNE_POINT &&
            point.id <= AstrologyConstants.WORK_POINT) {
          final signDegree = point.longitude % 30;
          text.writeln(
            '${point.name}: ${point.sign} ${formatDegree(signDegree)}, ${point.getHouseText()}',
          );
        }
      }
    }

    return text.toString();
  }

  /// 複製星盤資訊
  /// 將星盤資訊複製到剪貼簿。
  ///
  /// [options] 複製選項，用於控制要包含哪些信息
  Future<bool> copyChartInfo({CopyOptions? options}) async {
    if (_chartData.planets == null || _chartData.houses == null) return false;

    setCopying(true);

    try {
      // 生成要複製的文本，傳入用戶選擇的選項
      final String chartInfoText =
          await generateChartInfoText(options: options);

      // 複製到剪貼板
      await FlutterClipboard.copy(chartInfoText);
      return true;
    } catch (e) {
      logger.e('複製失敗: $e');
      return false;
    } finally {
      setCopying(false);
    }
  }

  /// 複製主題相關的星盤資訊
  /// 根據選擇的占星主題，複製相關的星盤資訊到剪貼簿
  ///
  /// [themeKey] 主題鍵值
  /// [themeInfo] 主題資訊
  /// [options] 複製選項
  Future<bool> copyThemeChartInfo({
    required String themeKey,
    required dynamic themeInfo,
    required CopyOptions options,
  }) async {
    if (_chartData.planets == null || _chartData.houses == null) return false;

    setCopying(true);

    try {
      // 生成主題相關的星盤資訊文本
      final String chartInfoText = await generateThemeChartInfoText(
        themeKey: themeKey,
        themeInfo: themeInfo,
        options: options,
      );

      // 複製到剪貼板
      await FlutterClipboard.copy(chartInfoText);
      return true;
    } catch (e) {
      logger.e('複製主題星盤資訊失敗: $e');
      return false;
    } finally {
      setCopying(false);
    }
  }

  /// 生成主題相關的星盤資訊文本
  /// 根據選擇的占星主題，生成相關的星盤資訊
  Future<String> generateThemeChartInfoText({
    required String themeKey,
    required dynamic themeInfo,
    required CopyOptions options,
  }) async {
    final StringBuffer text = StringBuffer();

    // 主題標題
    text.writeln('=' * 50);
    text.writeln('占星主題分析：${themeInfo.title}');
    text.writeln('分析重點：${themeInfo.subtitle}');
    text.writeln('=' * 50);
    text.writeln();

    // 主題描述
    text.writeln('【主題說明】');
    text.writeln(themeInfo.description);
    text.writeln();

    // 分析要點
    text.writeln('【分析要點】');
    for (int i = 0; i < themeInfo.keyPoints.length; i++) {
      text.writeln('${i + 1}. ${themeInfo.keyPoints[i]}');
    }
    text.writeln();

    // 生成基本星盤資訊
    final String basicChartInfo = await generateChartInfoText(options: options);
    text.writeln('【星盤資料】');
    text.writeln(basicChartInfo);

    // 根據不同主題添加特殊分析
    text.writeln();
    text.writeln('【主題重點分析】');
    text.writeln(_generateThemeSpecificAnalysis(themeKey, themeInfo));

    return text.toString();
  }

  /// 生成主題特定的分析內容
  String _generateThemeSpecificAnalysis(String themeKey, dynamic themeInfo) {
    final StringBuffer analysis = StringBuffer();

    switch (themeKey) {
      case 'relationship':
        analysis.writeln(_generateRelationshipAnalysis());
        break;
      case 'career':
        analysis.writeln(_generateCareerAnalysis());
        break;
      case 'self_exploration':
        analysis.writeln(_generateSelfExplorationAnalysis());
        break;
      case 'annual_fortune':
        analysis.writeln(_generateAnnualFortuneAnalysis());
        break;
      case 'wealth':
        analysis.writeln(_generateWealthAnalysis());
        break;
      default:
        analysis.writeln('此主題的詳細分析功能正在開發中...');
    }

    return analysis.toString();
  }

  /// 生成感情關係分析
  String _generateRelationshipAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【感情關係重點】');
    analysis.writeln('• 第7宮分析：伴侶關係與婚姻狀況');
    analysis.writeln('• 第5宮分析：戀愛模式與浪漫表達');
    analysis.writeln('• 第8宮分析：深度關係與親密連結');
    analysis.writeln('• 金星位置：愛情觀與吸引力特質');
    analysis.writeln('• 火星位置：激情表達與行動模式');
    analysis.writeln('• 月亮位置：情感需求與安全感來源');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 金星與火星的相位關係');
    analysis.writeln('• 第7宮宮主星的位置與相位');
    analysis.writeln('• 月亮與太陽的相位（情感與自我的協調）');
    analysis.writeln('• 互容接納關係（伴侶間的理解與支持）');

    return analysis.toString();
  }

  /// 生成職涯分析
  String _generateCareerAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【職涯發展重點】');
    analysis.writeln('• 第10宮分析：事業方向與社會地位');
    analysis.writeln('• 第6宮分析：工作環境與日常職務');
    analysis.writeln('• 第2宮分析：收入來源與價值觀');
    analysis.writeln('• 中天星座：職業形象與目標');
    analysis.writeln('• 太陽位置：領導能力與核心才能');
    analysis.writeln('• 土星位置：責任感與專業發展');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第10宮宮主星的位置與相位');
    analysis.writeln('• 太陽與土星的相位關係');
    analysis.writeln('• 水星位置（溝通能力）');
    analysis.writeln('• 木星位置（發展機會）');

    return analysis.toString();
  }

  /// 生成自我探索分析
  String _generateSelfExplorationAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【自我探索重點】');
    analysis.writeln('• 第1宮分析：外在表現與人格面具');
    analysis.writeln('• 第12宮分析：潛意識與內在陰影');
    analysis.writeln('• 太陽位置：核心自我與生命目標');
    analysis.writeln('• 月亮位置：內在需求與情感模式');
    analysis.writeln('• 上升點：外在形象與第一印象');
    analysis.writeln('• 元素分布：性格傾向與能量特質');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 太陽與月亮的相位（意識與潛意識的整合）');
    analysis.writeln('• 困難相位（成長挑戰與突破點）');
    analysis.writeln('• 行星尊貴力量（天賦與弱點）');
    analysis.writeln('• 日夜盤特質（能量表達方式）');

    return analysis.toString();
  }

  /// 生成流年運勢分析
  String _generateAnnualFortuneAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【流年運勢重點】');
    analysis.writeln('• 本命盤基礎：個人天賦與挑戰');
    analysis.writeln('• 行星週期：重要行星的運行影響');
    analysis.writeln('• 相位觸發：流年行星與本命行星的互動');
    analysis.writeln('• 宮位激活：不同生活領域的重點時期');
    analysis.writeln('• 時機選擇：重要決策的最佳時間點');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 土星與木星的運行週期');
    analysis.writeln('• 外行星（天王星、海王星、冥王星）的長期影響');
    analysis.writeln('• 日月食對個人星盤的觸發');
    analysis.writeln('• 重要相位的形成與分離時間');

    return analysis.toString();
  }

  /// 生成財富分析
  String _generateWealthAnalysis() {
    final StringBuffer analysis = StringBuffer();

    analysis.writeln('【財富能量重點】');
    analysis.writeln('• 第2宮分析：個人財富與價值觀');
    analysis.writeln('• 第8宮分析：他人資源與投資理財');
    analysis.writeln('• 第11宮分析：收益來源與財富增長');
    analysis.writeln('• 金星位置：金錢觀與消費模式');
    analysis.writeln('• 木星位置：財富擴展與機會');
    analysis.writeln('• 土星位置：儲蓄能力與財務紀律');
    analysis.writeln();
    analysis.writeln('【建議關注】');
    analysis.writeln('• 第2宮與第8宮宮主星的相位');
    analysis.writeln('• 金星與木星的相位關係');
    analysis.writeln('• 土星與冥王星的財務影響');
    analysis.writeln('• 財富相關阿拉伯點的位置');

    return analysis.toString();
  }

  /// 生成包含星盤資訊的 PDF 報告。
  ///
  /// 返回值：PDF 文件的字節數據，如果生成失敗則返回 null
  /// 如果生成過程中發生錯誤，會拋出異常並包含錯誤信息
  Future<Uint8List?> generatePdf() async {
    if (_chartData.planets == null || _chartData.houses == null) {
      logger.w('無法生成 PDF：行星或宮位數據為空');
      return null;
    }

    setGeneratingPdf(true);

    try {
      // 檢查數據是否完整
      if (_chartData.aspects == null || _chartData.aspects!.isEmpty) {
        logger.w('相位數據為空，但仍然繼續生成 PDF');
      }

      // 記錄開始生成 PDF 的時間
      final startTime = DateTime.now();
      logger.i('開始生成 PDF');

      // 生成 PDF
      final pdfBytes = await ChartPdfGenerator.generatePdf(
        birthData: _primaryPerson,
        planets: _chartData.planets!,
        aspects: _chartData.aspects ?? [],
        housesData: _chartData.houses!,
        latitude: _latitude,
        longitude: _longitude,
      );

      // 計算生成時間
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      logger.i(
          'PDF 生成成功，大小: ${pdfBytes.length} 字節，耗時: ${duration.inMilliseconds} 毫秒');
      return pdfBytes;
    } catch (e, stackTrace) {
      logger.e('生成 PDF 失敗: $e');
      logger.e('堆疊追蹤: $stackTrace');

      // 將錯誤信息向上拋出，以便在 UI 中顯示
      if (e.toString().contains('TooManyPagesException')) {
        throw Exception('生成的 PDF 頁數超出限制，請減少內容或聯繫開發者。');
      } else if (e.toString().contains('OutOfMemoryError') ||
          e.toString().contains('memory')) {
        throw Exception('生成 PDF 時記憶體不足，請重新啟動應用或聯繫開發者。');
      } else if (e.toString().contains('timeout') ||
          e.toString().contains('timed out')) {
        throw Exception('生成 PDF 時逾時，請重試或聯繫開發者。');
      }
      throw Exception('生成 PDF 時發生錯誤: ${e.toString()}');
    } finally {
      setGeneratingPdf(false);
    }
  }

  // 發送包含星盤數據的電子郵件。
  Future<bool> sendEmail({required String email, required bool useHtml}) async {
    setSendingEmail(true);

    try {
      final bool success = await ChartEmailSender.sendChartData(
        birthData: _primaryPerson,
        planets: _chartData.planets!,
        aspects: _chartData.aspects!,
        housesData: _chartData.houses!,
        recipientEmail: email,
        useHtml: useHtml,
        latitude: _latitude,
        longitude: _longitude,
      );

      return success;
    } catch (e) {
      logger.e('發送失敗: $e');
      return false;
    } finally {
      setSendingEmail(false);
    }
  }

  /// 獲取 AI 對星盤的解讀
  ///
  /// 根據星盤類型選擇適當的解讀方法：
  /// - 對於關係盤（合盤類）：使用雙圈盤解讀方法
  /// - 對於其他星盤類型：使用單圈盤解讀方法
  ///
  /// 返回值：解讀是否成功（是否有內容）
  Future<bool> getAIInterpretation() async {
    // 檢查必要的數據是否存在
    if (_chartData.planets == null ||
        _chartData.planets!.isEmpty ||
        _chartData.aspects == null ||
        _chartData.aspects!.isEmpty) {
      logger.w('無法獲取 AI 解讀：行星或相位數據為空');
      return false;
    }

    try {
      // 使用 ChartType 的 isRelationshipChart 屬性來判斷是否為關係盤
      if (_chartType.isRelationshipChart && _secondaryPerson != null) {
        logger.i('使用雙圈盤解讀方法解讀${_chartType.name}');
        // 使用雙圈盤解讀方法
        await _interpretationViewModel.getDualChartInterpretation(_chartData);
      } else {
        logger.i('使用單圈盤解讀方法解讀${_chartType.name}');
        // 使用單圈盤解讀方法
        await _interpretationViewModel.getInterpretation(_chartData);
      }

      // 檢查解讀結果是否有內容
      final hasInterpretation =
          _interpretationViewModel.interpretation.isNotEmpty;
      logger.i('AI 解讀${hasInterpretation ? "成功" : "失敗"}');
      return hasInterpretation;
    } catch (e) {
      logger.e('獲取 AI 解讀失敗: $e');
      return false;
    }
  }

  /// 根據宮頭經度獲取宮主星
  String _getHouseRuler(double cuspLongitude) {
    // 獲取宮頭所在星座
    final signIndex = (cuspLongitude / 30).floor() % 12;

    // 根據星座確定宮主
    switch (signIndex) {
      case 0:
        return '火星'; // 牡羊座
      case 1:
        return '金星'; // 金牛座
      case 2:
        return '水星'; // 雙子座
      case 3:
        return '月亮'; // 巨蟹座
      case 4:
        return '太陽'; // 獅子座
      case 5:
        return '水星'; // 處女座
      case 6:
        return '金星'; // 天秤座
      case 7:
        return '火星'; // 天蠍座
      case 8:
        return '木星'; // 射手座
      case 9:
        return '土星'; // 摩羊座
      case 10:
        return '土星'; // 水瓶座
      case 11:
        return '木星'; // 雙魚座
      default:
        return '未知';
    }
  }

  /// 根據星座索引獲取宮主星
  String _getHouseRulerBySignIndex(int signIndex) {
    // 根據星座確定宮主
    switch (signIndex) {
      case 0:
        return '火星'; // 牡羊座
      case 1:
        return '金星'; // 金牛座
      case 2:
        return '水星'; // 雙子座
      case 3:
        return '月亮'; // 巨蟹座
      case 4:
        return '太陽'; // 獅子座
      case 5:
        return '水星'; // 處女座
      case 6:
        return '金星'; // 天秤座
      case 7:
        return '火星'; // 天蠍座
      case 8:
        return '木星'; // 射手座
      case 9:
        return '土星'; // 摩羊座
      case 10:
        return '土星'; // 水瓶座
      case 11:
        return '木星'; // 雙魚座
      default:
        return '未知';
    }
  }

  /// 根據星座索引獲取行星 ID
  int _getPlanetIdBySignIndex(int signIndex) {
    // 根據星座確定行星 ID
    switch (signIndex) {
      case 0:
        return AstrologyConstants.MARS; // 牡羊座 - 火星
      case 1:
        return AstrologyConstants.VENUS; // 金牛座 - 金星
      case 2:
        return AstrologyConstants.MERCURY; // 雙子座 - 水星
      case 3:
        return AstrologyConstants.MOON; // 巨蟹座 - 月亮
      case 4:
        return AstrologyConstants.SUN; // 獅子座 - 太陽
      case 5:
        return AstrologyConstants.MERCURY; // 處女座 - 水星
      case 6:
        return AstrologyConstants.VENUS; // 天秤座 - 金星
      case 7:
        return AstrologyConstants.MARS; // 天蠍座 - 火星
      case 8:
        return AstrologyConstants.JUPITER; // 射手座 - 木星
      case 9:
        return AstrologyConstants.SATURN; // 摩羯座 - 土星
      case 10:
        return AstrologyConstants.SATURN; // 水瓶座 - 土星
      case 11:
        return AstrologyConstants.JUPITER; // 雙魚座 - 木星
      default:
        return -1;
    }
  }

  /// 計算元素分佈（火土風水）
  Map<String, int> _calculateElementStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '火': 0,
      '土': 0,
      '風': 0,
      '水': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        // 使用 AstrologyConstants 中的常量來確定元素
        final element = AstrologyConstants.SIGN_ELEMENTS[planet.sign];
        if (element != null) {
          stats[element] = (stats[element] ?? 0) + 1;
        }
      }
    }

    return stats;
  }

  /// 計算品質分佈（啟動、固定、變動）
  Map<String, int> _calculateModalityStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '啟動': 0,
      '固定': 0,
      '變動': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        // 使用 AstrologyConstants 中的常量來確定品質
        final signIndex = AstrologyConstants.ZODIAC_SIGNS.indexOf(planet.sign);
        if (signIndex != -1) {
          final modality = AstrologyConstants.SIGN_MODALITIES[planet.sign];
          if (modality != null) {
            // 將英文品質轉換為中文
            String modalityZh = '';
            switch (modality) {
              case 'cardinal':
                modalityZh = '啟動';
                break;
              case 'fixed':
                modalityZh = '固定';
                break;
              case 'mutable':
                modalityZh = '變動';
                break;
            }
            if (modalityZh.isNotEmpty) {
              stats[modalityZh] = (stats[modalityZh] ?? 0) + 1;
            }
          }
        }
      }
    }

    return stats;
  }

  /// 計算陰陽分佈
  Map<String, int> _calculatePolarityStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '陽性': 0,
      '陰性': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        // 使用 AstrologyConstants 中的常量來確定陰陽性
        final isMasculine =
            AstrologyConstants.SIGN_POLARITY[planet.sign] ?? false;
        final polarityKey = isMasculine ? '陽性' : '陰性';
        stats[polarityKey] = (stats[polarityKey] ?? 0) + 1;
      }
    }

    return stats;
  }

  // 已移除不再需要的 _getElementFromSign 和 _getModalityFromSign 方法
  // 現在直接使用 AstrologyConstants 中的常量

  /// 按元素分組行星
  Map<String, List<PlanetPosition>> _getPlanetsByElement(
      List<PlanetPosition> planets) {
    final Map<String, List<PlanetPosition>> elementPlanets = {
      '火': [],
      '土': [],
      '風': [],
      '水': [],
    };

    // 只考慮主要行星（太陽到冥王星）
    for (final planet in planets) {
      if (planet.id >= 0 && planet.id <= 9) {
        // 使用 AstrologyConstants 中的常量來確定元素
        final element = AstrologyConstants.SIGN_ELEMENTS[planet.sign];
        if (element != null) {
          elementPlanets[element]!.add(planet);
        }
      }
    }

    return elementPlanets;
  }

  /// 格式化行星列表為文本
  String _formatPlanetList(List<PlanetPosition> planets) {
    if (planets.isEmpty) return '無';

    return planets.map((planet) => '${planet.name}').join(', ');
  }

  // 獲取宮位顏色
  // 根據宮位元素屬性獲取顏色。
  Color getHouseColor(int houseNumber) {
    // 根據宮位的元素屬性分配顏色
    switch (houseNumber % 4) {
      case 1: // 火象宮 (1, 5, 9)
        return Colors.red[700]!;
      case 2: // 土象宮 (2, 6, 10)
        return Colors.brown[600]!;
      case 3: // 風象宮 (3, 7, 11)
        return Colors.blue[600]!;
      case 0: // 水象宮 (4, 8, 12)
        return Colors.teal[600]!;
      default:
        return Colors.grey[700]!;
    }
  }

  /// 根據相位類型返回對應的顏色
  Color getAspectColor(String aspectType) {
    switch (aspectType) {
      case '合相':
        return const Color(0xFF224EA5); // 藍色
      case '六分相':
        return const Color(0xFF2999A4); // 淺藍色
      case '四分相':
        return Colors.red; // 紅色
      case '三分相':
        return Colors.green; // 綠色
      case '對分相':
        return const Color(0xFF051883); // 深藍色
      case '接納':
        return Colors.purple; // 紫色
      default:
        return Colors.grey; // 灰色
    }
  }

  /// 計算界主星配置法的完整時間表
  ///
  /// 根據出生資料計算界主星配置法的時間表
  Future<TermRulerTimelineResult> calculateTermRulerProgressionTimeline() async {
    try {
      // 獲取出生資料
      final birthDateTime = _chartData.primaryPerson.birthDate;
      final latitude = _chartData.primaryPerson.latitude;
      final longitude = _chartData.primaryPerson.longitude;

      // 計算宮位數據以獲取上升點
      if (_chartData.houses == null || _chartData.houses!.ascmc.isEmpty) {
        throw Exception('無法獲取宮位數據');
      }

      final double ascendantLongitude = _chartData.houses!.ascmc[0];
      final bool isNorth = latitude >= 0;

      // 計算界主星配置法
      final TermRulerProgressionResult termRulerProgression = _astrologyService.calculateTermRulerProgression(
        ascendantLongitude,
        latitude,
        isNorth
      );

      // 計算完整的時間表
      final List<TermRulerTimelineItem> timeline = calculateFullTermRulerTimeline(
        ascendantLongitude,
        latitude,
        isNorth,
        birthDateTime
      );

      return TermRulerTimelineResult(
        currentInfo: termRulerProgression,
        timeline: timeline,
        birthDateTime: birthDateTime,
        ascendantLongitude: ascendantLongitude,
        latitude: latitude,
        longitude: longitude,
      );
    } catch (e) {
      logger.e('計算界主星配置法時間表失敗: $e');
      rethrow;
    }
  }

  /// 計算完整的界主星配置法時間表（逆時針方向 - 度數遞增，完整圈）
  List<TermRulerTimelineItem> calculateFullTermRulerTimeline(
    double ascendantLongitude,
    double latitude,
    bool isNorth,
    DateTime birthDateTime
  ) {
    final List<TermRulerTimelineItem> timeline = [];
    DateTime currentDateTime = birthDateTime;

    // 計算完整的360度循環
    double currentLongitude = ascendantLongitude;
    final double startLongitude = ascendantLongitude;
    bool isFirstTerm = true;

    // 計算完整圈（360度）
    while (currentLongitude < startLongitude + 360.0) {
      final String currentSign = getZodiacSign(currentLongitude);
      final double degreeInSign = currentLongitude % 30.0;

      // 獲取該星座的界主星定義
      final Map<String, int>? terms = AstrologyConstants.ZODIAC_TERMS[currentSign];
      if (terms == null) {
        currentLongitude += 1.0; // 跳過無效星座
        continue;
      }

      // 獲取時間數據
      final List<double> timeData = AscensionTable.getTimeByLatitudeAndSign(
        latitude,
        currentSign,
        isNorth
      );
      final double timePerDegree = timeData[1]; // 每度所需時間（天）

      // 將字符串key轉換為double並正序排列（逆時針 = 度數遞增）
      final List<double> degrees = terms.keys
          .map((key) => double.parse(key))
          .toList()
        ..sort(); // 正序排列：0, 6, 12, 20, 25

      // 找到當前度數所在的界
      double? currentTermStart;
      double? nextTermStart;
      int? currentTermRuler;

      for (int i = 0; i < degrees.length; i++) {
        if (degreeInSign >= degrees[i]) {
          currentTermStart = degrees[i];
          currentTermRuler = terms[degrees[i].toString()];
          // 找到下一個界的起始度數
          if (i < degrees.length - 1) {
            nextTermStart = degrees[i + 1];
          } else {
            nextTermStart = 30.0; // 星座結束
          }
        }
      }

      if (currentTermStart != null && nextTermStart != null && currentTermRuler != null) {
        // 計算這個界的剩餘度數
        final double remainingDegrees = nextTermStart - degreeInSign;
        final double remainingDays = remainingDegrees * timePerDegree;

        // 使用精確的分鐘計算
        final remainingMinutes = remainingDays * 24 * 60;
        final endDateTime = currentDateTime.add(Duration(minutes: remainingMinutes.round()));

        // 計算星座總時間（檢查是否為星座切換）
        double? signTotalTime;
        bool isSignChange = false;

        // 檢查是否為星座的第一個界或接近星座邊界
        if (degreeInSign <= 1.0 || (currentLongitude % 30.0) <= 1.0) {
          // 獲取這個星座的總時間
          final List<double> signTimeData = AscensionTable.getTimeByLatitudeAndSign(
            latitude,
            currentSign,
            isNorth
          );
          signTotalTime = signTimeData[0]; // 走完整個星座所需時間（天）
          isSignChange = true;
        }

        timeline.add(TermRulerTimelineItem(
          termRuler: currentTermRuler,
          termRulerName: _getPlanetNameById(currentTermRuler),
          timePerDegree : timePerDegree,
          startDegree: degreeInSign,
          endDegree: nextTermStart,
          startDateTime: currentDateTime,
          endDateTime: endDateTime,
          durationDays: remainingDays,
          isCurrentTerm: isFirstTerm,
          sign: currentSign,
          direction: '逆時針',
          longitude: currentLongitude,
          signTotalTime: signTotalTime, // 星座總時間
          isSignChange: isSignChange, // 是否為星座切換
        ));

        currentDateTime = endDateTime;
        currentLongitude += remainingDegrees;
        isFirstTerm = false;
      } else {
        // 如果無法找到界，跳到下一度
        currentLongitude += 1.0;
      }

      // 防止無限循環
      if (timeline.length > 100) break;
    }

    return timeline;
  }

  /// 獲取上一個星座（逆時針方向）
  String _getPreviousZodiacSign(String currentSign) {
    final signs = AstrologyConstants.ZODIAC_SIGNS;
    final int currentIndex = signs.indexOf(currentSign);
    if (currentIndex == -1) return signs[11]; // 返回雙魚座

    final int prevIndex = (currentIndex - 1 + signs.length) % signs.length;
    return signs[prevIndex];
  }

  /// 根據行星ID獲取行星名稱
  String _getPlanetNameById(int planetId) {
    for (final planet in AstrologyConstants.PLANETS) {
      if (planet['id'] == planetId) {
        return planet['name'] as String;
      }
    }
    return '未知行星';
  }
}
