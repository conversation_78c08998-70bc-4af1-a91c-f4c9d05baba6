import 'package:flutter/material.dart';
import '../viewmodels/chart_viewmodel.dart';
import 'term_ruler_progression_card.dart';

/// 可拖動的界主星配置法卡片
class DraggableTermRulerProgressionCard extends StatefulWidget {
  final Map<String, dynamic> result;
  final ChartViewModel viewModel;
  final VoidCallback onClose;
  final Offset initialPosition;

  const DraggableTermRulerProgressionCard({
    Key? key,
    required this.result,
    required this.viewModel,
    required this.onClose,
    required this.initialPosition,
  }) : super(key: key);

  @override
  State<DraggableTermRulerProgressionCard> createState() => _DraggableTermRulerProgressionCardState();
}

class _DraggableTermRulerProgressionCardState extends State<DraggableTermRulerProgressionCard> {
  late Offset _position;
  bool _isDragging = false;
  double _opacity = 1.0;

  @override
  void initState() {
    super.initState();
    _position = widget.initialPosition;
  }

  @override
  Widget build(BuildContext context) {
    // 確保卡片不會超出螢幕範圍
    final screenSize = MediaQuery.of(context).size;
    const cardWidth = 320.0; // 卡片寬度
    final cardHeight = screenSize.height * 0.8; // 卡片高度上限

    // 限制卡片位置在螢幕內
    double safeX = _position.dx;
    double safeY = _position.dy;

    // 確保卡片不會超出右邊界
    if (safeX + cardWidth > screenSize.width) {
      safeX = screenSize.width - cardWidth - 10;
    }

    // 確保卡片不會超出左邊界
    if (safeX < 10) {
      safeX = 10;
    }

    // 確保卡片不會超出下邊界
    if (safeY + cardHeight > screenSize.height) {
      safeY = screenSize.height - cardHeight - 10;
    }

    // 確保卡片不會超出上邊界
    if (safeY < 10) {
      safeY = 10;
    }

    return Positioned(
      left: safeX,
      top: safeY,
      child: GestureDetector(
        onPanStart: (details) {
          setState(() {
            _isDragging = true;
            _opacity = 0.7; // 拖動時變透明
          });
        },
        onPanUpdate: (details) {
          setState(() {
            _position = Offset(
              _position.dx + details.delta.dx,
              _position.dy + details.delta.dy,
            );
          });
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
            _opacity = 1.0; // 停止拖動時恢復不透明
          });
        },
        onTap: () {
          // 點擊卡片時切換透明度
          setState(() {
            _opacity = _opacity == 1.0 ? 0.3 : 1.0;
          });
        },
        child: AnimatedOpacity(
          opacity: _opacity,
          duration: const Duration(milliseconds: 200),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _isDragging 
                  ? Colors.deepPurple.withOpacity(0.5) 
                  : Colors.transparent,
                width: _isDragging ? 2 : 0,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(_isDragging ? 0.3 : 0.15),
                  blurRadius: _isDragging ? 25 : 20,
                  offset: Offset(0, _isDragging ? 12 : 8),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: TermRulerProgressionCard(
                result: widget.result,
                viewModel: widget.viewModel,
                onClose: widget.onClose,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
