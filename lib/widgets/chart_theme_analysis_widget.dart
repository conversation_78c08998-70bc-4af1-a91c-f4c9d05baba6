import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/chart_theme_analysis_result.dart';
import '../viewmodels/chart_viewmodel.dart';

/// 星盤主題分析組件
/// 
/// 提供七大主題的星盤分析功能
class ChartThemeAnalysisWidget extends StatefulWidget {
  final ChartViewModel viewModel;

  const ChartThemeAnalysisWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<ChartThemeAnalysisWidget> createState() => _ChartThemeAnalysisWidgetState();
}

class _ChartThemeAnalysisWidgetState extends State<ChartThemeAnalysisWidget> {
  ChartThemeType? _selectedTheme;
  ConsultationStyle? _selectedStyle;
  ChartThemeAnalysisResult? _analysisResult;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 標題
            Row(
              children: [
                Icon(
                  Icons.psychology,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  '星盤主題分析',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 主題選擇
            Text(
              '選擇分析主題：',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 120, // 限制高度
              child: SingleChildScrollView(
                child: Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: ChartThemeType.values.map((theme) {
                    return ChoiceChip(
                      label: Text(
                        theme.name,
                        style: TextStyle(
                          fontSize: 12,
                          color: _selectedTheme == theme ? Colors.white : null,
                        ),
                      ),
                      selected: _selectedTheme == theme,
                      onSelected: (selected) {
                        setState(() {
                          _selectedTheme = selected ? theme : null;
                          _analysisResult = null;
                        });
                      },
                      selectedColor: Theme.of(context).primaryColor,
                    );
                  }).toList(),
                ),
              ),
            ),

            // 風格選擇（僅在選擇諮詢風格主題時顯示）
            if (_selectedTheme == ChartThemeType.consultationStyles) ...[
              const SizedBox(height: 16),
              Text(
                '選擇諮詢風格：',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 80, // 限制高度
                child: SingleChildScrollView(
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: ConsultationStyle.values.map((style) {
                      return ChoiceChip(
                        label: Text(
                          style.name,
                          style: TextStyle(
                            fontSize: 12,
                            color: _selectedStyle == style ? Colors.white : null,
                          ),
                        ),
                        selected: _selectedStyle == style,
                        onSelected: (selected) {
                          setState(() {
                            _selectedStyle = selected ? style : null;
                          });
                        },
                        selectedColor: Theme.of(context).primaryColor,
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],

            const SizedBox(height: 16),

            // 分析按鈕
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedTheme != null && !_isLoading
                    ? _performAnalysis
                    : null,
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('開始分析'),
              ),
            ),

            // 分析結果
            if (_analysisResult != null) ...[
              const SizedBox(height: 24),
              const Divider(),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: _buildAnalysisResult(),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 執行主題分析
  Future<void> _performAnalysis() async {
    if (_selectedTheme == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await widget.viewModel.generateThemeAnalysis(
        _selectedTheme!,
        consultationStyle: _selectedStyle,
      );

      setState(() {
        _analysisResult = result;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分析失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 建構分析結果顯示
  Widget _buildAnalysisResult() {
    if (_analysisResult == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分析標題和複製按鈕
        Row(
          children: [
            Expanded(
              child: Text(
                _analysisResult!.title,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            IconButton(
              onPressed: _copyAnalysisResult,
              icon: const Icon(Icons.copy),
              tooltip: '複製分析結果',
              style: IconButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                foregroundColor: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // 星盤基本資訊
        _buildChartInfo(),
        const SizedBox(height: 16),

        // 分析摘要
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            _analysisResult!.summary,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        const SizedBox(height: 16),

        // 關鍵要素
        if (_analysisResult!.keyElements.isNotEmpty) ...[
          Text(
            '關鍵要素：',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _analysisResult!.keyElements.map((element) {
              return Chip(
                label: Text(
                  element,
                  style: const TextStyle(fontSize: 12),
                ),
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
        ],

        // 詳細分析章節
        Text(
          '詳細分析：',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ..._analysisResult!.sections.map((section) => _buildSection(section)),

        // 建議與指引
        if (_analysisResult!.recommendations.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            '建議與指引：',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...(_analysisResult!.recommendations.asMap().entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${entry.key + 1}. ',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      entry.value,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            );
          })),
        ],
      ],
    );
  }

  /// 建構分析章節
  Widget _buildSection(ThemeAnalysisSection section) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        title: Row(
          children: [
            // 重要程度指示器
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: _getImportanceColor(section.importance),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                section.title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  section.content,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (section.relatedPlanets.isNotEmpty ||
                    section.relatedHouses.isNotEmpty ||
                    section.relatedAspects.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  const Divider(),
                  const SizedBox(height: 8),
                  if (section.relatedPlanets.isNotEmpty)
                    _buildRelatedInfo('相關行星', section.relatedPlanets),
                  if (section.relatedHouses.isNotEmpty)
                    _buildRelatedInfo('相關宮位', section.relatedHouses.map((h) => '第${h}宮').toList()),
                  if (section.relatedAspects.isNotEmpty)
                    _buildRelatedInfo('相關相位', section.relatedAspects),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 建構相關資訊顯示
  Widget _buildRelatedInfo(String title, List<String> items) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$title：',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              items.join('、'),
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  /// 根據重要程度獲取顏色
  Color _getImportanceColor(int importance) {
    switch (importance) {
      case 5:
        return Colors.red;
      case 4:
        return Colors.orange;
      case 3:
        return Colors.yellow;
      case 2:
        return Colors.blue;
      case 1:
      default:
        return Colors.grey;
    }
  }

  /// 建構星盤基本資訊
  Widget _buildChartInfo() {
    return FutureBuilder<String>(
      future: widget.viewModel.generateChartInfoText(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.2),
              ),
            ),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.red.withOpacity(0.2),
              ),
            ),
            child: Text(
              '載入星盤資訊時發生錯誤：${snapshot.error}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        final chartInfoText = snapshot.data ?? '';

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).primaryColor.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 星盤資訊標題
              Row(
                children: [
                  Icon(
                    Icons.star,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '完整星盤資訊',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 顯示完整的星盤資訊文本
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  chartInfoText,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 建構資訊行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label：',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 複製分析結果
  Future<void> _copyAnalysisResult() async {
    if (_analysisResult == null) return;

    final chartData = widget.viewModel.chartData;
    final person = chartData?.primaryPerson;

    final buffer = StringBuffer();

    // 添加標題
    buffer.writeln('=' * 50);
    buffer.writeln(_analysisResult!.title);
    buffer.writeln('=' * 50);
    buffer.writeln();

    // 添加星盤基本資訊
    if (person != null) {
      buffer.writeln('【星盤基本資訊】');
      buffer.writeln('姓名：${person.name}');
      buffer.writeln('出生日期：${_formatDateTime(person.birthDate)}');
      buffer.writeln('出生地點：${person.birthPlace}');
      buffer.writeln('經緯度：${person.longitude.toStringAsFixed(2)}°E, ${person.latitude.toStringAsFixed(2)}°N');
      buffer.writeln();

      // 添加重要行星位置
      final planets = chartData?.planets ?? [];
      if (planets.isNotEmpty) {
        buffer.writeln('【重要行星位置】');
        for (final planet in planets.take(10)) {
          buffer.writeln('${planet.name}：${planet.sign} ${planet.longitude.toStringAsFixed(1)}° 第${planet.house}宮');
        }
        buffer.writeln();
      }
    }

    // 添加分析摘要
    buffer.writeln('【分析摘要】');
    buffer.writeln(_analysisResult!.summary);
    buffer.writeln();

    // 添加關鍵要素
    if (_analysisResult!.keyElements.isNotEmpty) {
      buffer.writeln('【關鍵要素】');
      for (final element in _analysisResult!.keyElements) {
        buffer.writeln('• $element');
      }
      buffer.writeln();
    }

    // 添加詳細分析
    buffer.writeln('【詳細分析】');
    for (final section in _analysisResult!.sections) {
      buffer.writeln();
      buffer.writeln('◆ ${section.title}');
      buffer.writeln('-' * 30);
      buffer.writeln(section.content);

      if (section.relatedPlanets.isNotEmpty) {
        buffer.writeln('相關行星：${section.relatedPlanets.join('、')}');
      }
      if (section.relatedHouses.isNotEmpty) {
        buffer.writeln('相關宮位：${section.relatedHouses.map((h) => '第${h}宮').join('、')}');
      }
      if (section.relatedAspects.isNotEmpty) {
        buffer.writeln('相關相位：${section.relatedAspects.join('、')}');
      }
    }

    // 添加建議與指引
    if (_analysisResult!.recommendations.isNotEmpty) {
      buffer.writeln();
      buffer.writeln('【建議與指引】');
      for (int i = 0; i < _analysisResult!.recommendations.length; i++) {
        buffer.writeln('${i + 1}. ${_analysisResult!.recommendations[i]}');
      }
    }

    // 添加分析時間
    buffer.writeln();
    buffer.writeln('分析時間：${_formatDateTime(_analysisResult!.analysisTime)}');
    buffer.writeln('=' * 50);

    // 複製到剪貼板
    try {
      await Clipboard.setData(ClipboardData(text: buffer.toString()));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('分析結果已複製到剪貼板'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('複製失敗：$e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }
}
