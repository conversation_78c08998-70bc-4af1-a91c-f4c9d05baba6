import 'package:astreal/models/aspect_info.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/models/term_ruler_timeline_result.dart';
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:astreal/widgets/chart_painter.dart';
import 'package:astreal/widgets/chart_theme_analysis_widget.dart';
import 'package:astreal/widgets/draggable_planet_detail_card.dart';
import 'package:astreal/widgets/draggable_term_ruler_progression_card.dart';
import 'package:astreal/widgets/dual_chart_painter.dart';
import 'package:astreal/widgets/firdaria_chart_painter.dart';
import 'package:astreal/widgets/person_info_widget.dart';
import 'package:astreal/widgets/time_adjustment_widget.dart';
import 'package:flutter/material.dart';

import '../models/planet_position.dart';

class ChartViewWidget extends StatefulWidget {
  final ChartViewModel viewModel;

  const ChartViewWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<ChartViewWidget> createState() => _ChartViewWidgetState();
}

class _ChartViewWidgetState extends State<ChartViewWidget> {
  // 存儲最後點擊的行星
  PlanetPosition? _lastHitPlanet;

  // 用於顯示行星詳情卡片的 OverlayEntry
  OverlayEntry? _overlayEntry;

  // 緩存 CustomPainter 實例
  CustomPainter? _cachedPainter;

  /// 加載法達盤數據
  Future<void> _loadFirdariaData() async {
    try {
      // 計算法達盤數據
      await widget.viewModel.calculateFirdaria();

      // 數據加載完成後滾動到當前週期
      if (mounted) {
        _getChartPainter();
      }
    } catch (e) {
      print('加載法達盤數據時出錯: $e');
    }
  }

  @override
  void didUpdateWidget(ChartViewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果圖表類型發生變化，清除緩存的 CustomPainter 實例
    if (oldWidget.viewModel.chartType != widget.viewModel.chartType ||
        oldWidget.viewModel.chartData != widget.viewModel.chartData) {
      print('didUpdateWidget: 清除緩存的 CustomPainter 實例');
      _cachedPainter = null;
    }

    if (widget.viewModel.chartType == ChartType.firdaria) {
      // 檢查是否已經有法達盤數據，如果沒有則計算
      if (widget.viewModel.firdariaData == null ||
          widget.viewModel.firdariaData!.isEmpty) {
        _loadFirdariaData();
      }
    }
  }

  @override
  void dispose() {
    // 確保在 widget 銷毀時移除 overlay
    _removeOverlay();
    super.dispose();
  }

  // 移除 overlay
  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  /// 顯示界主星配置法卡片（使用 Overlay）
  void _showTermRulerProgressionCard(BuildContext context, TermRulerTimelineResult result) {
    try {
      // 移除現有的 overlay（如果有的話）
      _removeOverlay();

      // 計算初始位置（螢幕中央偏右上）
      final screenSize = MediaQuery.of(context).size;
      final left = screenSize.width * 0.1;
      final top = screenSize.height * 0.1;

      // 創建並插入 overlay
      _overlayEntry = OverlayEntry(
        builder: (context) => Stack(
          children: [
            // 透明全屏底層，用於捕捉點擊事件
            Positioned.fill(
              child: GestureDetector(
                onTap: _removeOverlay, // 點擊空白處關閉卡片
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
            // 可拖動卡片
            DraggableTermRulerProgressionCard(
              result: result,
              viewModel: widget.viewModel,
              onClose: _removeOverlay,
              initialPosition: Offset(left, top),
            ),
          ],
        ),
      );

      // 插入 overlay
      Overlay.of(context).insert(_overlayEntry!);
      print('_showTermRulerProgressionCard: overlay inserted');
    } catch (e) {
      print('_showTermRulerProgressionCard error: $e');
    }
  }

  /// 顯示界主星配置法
  void _showTermRulerProgression(BuildContext context) async {
    try {
      // 顯示載入對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 計算界主星配置法時間表
      final TermRulerTimelineResult result =
          await widget.viewModel.calculateTermRulerProgressionTimeline();

      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示結果對話框
      if (mounted) {
        _showTermRulerProgressionCard(context, result);
      }
    } catch (e) {
      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤對話框
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('錯誤'),
            content: Text('計算界主星配置法失敗：$e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('確定'),
              ),
            ],
          ),
        );
      }
    }
  }

  /// 根據圖表類型決定使用哪種繪製器
  CustomPainter _getChartPainter() {
    print('_getChartPainter: 創建新的 CustomPainter 實例');
    final chartType = widget.viewModel.chartType;

    // 如果是法達盤，使用 FirdariaChartPainter
    if (chartType == ChartType.firdaria) {
      // 取得法達盤數據
      final firdariaData = widget.viewModel.firdariaData ?? [];
      final birthDate = widget.viewModel.chartData.primaryPerson.birthDate;
      final isDaytime = widget.viewModel.isDaytimeBirth;

      _cachedPainter = FirdariaChartPainter(
        widget.viewModel.chartData.planets!, // 行星數據
        widget.viewModel.chartData.aspects!,
        housesData: widget.viewModel.chartData.houses!,
        chartType: chartType,
        firdariaData: firdariaData,
        birthDate: birthDate,
        isDaytime: isDaytime,
        selectedPeriodIndex: widget.viewModel.selectedFirdariaPeriodIndex,
      );

      return _cachedPainter!;
    }

    // 需要雙圈顯示的圖表類型
    if (_needsDualChartPainter()) {
      // 根據不同的圖表類型選擇適當的行星數據
      if (chartType == ChartType.synastry ||
          chartType == ChartType.synastrySecondary ||
          chartType == ChartType.synastryTertiary) {
        // 比較盤：本命盤和次要人物的行星
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!, // 本命盤行星
          widget.viewModel.chartData.secondaryPerson!.planets!, // 次要人物行星
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
        );
      } else if (chartType == ChartType.transit) {
        // 行運盤：本命盤和當前行運行星
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!, // 本命盤行星
          widget.viewModel.chartData.planets!, // 行運盤行星
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
        );
      } else {
        // 其他雙圈圖表：使用相同的行星數據
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!, // 本命盤行星
          widget.viewModel.chartData.planets!, // 行運盤行星
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
        );
      }
    } else {
      _cachedPainter = ChartPainter(
        widget.viewModel.chartData.planets!, // 行星數據
        widget.viewModel.chartData.aspects!,
        housesData: widget.viewModel.chartData.houses!,
        chartType: chartType,
        chartSettings: widget.viewModel.chartSettings, // 傳入星盤設定
      );
    }

    return _cachedPainter!;
  }

  /// 判斷是否需要使用雙圈星盤繪製器
  bool _needsDualChartPainter() {
    final chartType = widget.viewModel.chartType;

    // 預測類星盤中的特定類型
    final progressionTypes = [
      ChartType.solarArcDirection,
    ];

    // 需要雙圈顯示的特殊類型
    final specialDualTypes = [
      ChartType.transit,
      ChartType.synastry,
    ];

    return progressionTypes.contains(chartType) ||
        specialDualTypes.contains(chartType) ||
        chartType.isSynastryProgression;
  }

  /// 獲取圖表標題
  String _getChartTitle() {
    switch (widget.viewModel.chartType) {
      case ChartType.equinoxSolstice:
        return widget.viewModel.chartData.primaryPerson.name;
      case ChartType.mundane:
      case ChartType.horary:
        return widget.viewModel.chartType.name;
      default:
        break;
    }
    if (widget.viewModel.chartType.requiresTwoPersons &&
        widget.viewModel.chartData.secondaryPerson != null) {
      return '${widget.viewModel.chartData.primaryPerson.name} 與 ${widget.viewModel.chartData.secondaryPerson!.name} 的${widget.viewModel.chartType.name}';
    } else {
      return '${widget.viewModel.chartData.primaryPerson.name}的${widget.viewModel.chartType.name}';
    }
  }

  /// 處理星盤點擊事件
  void _handleChartTap(
      BuildContext context, Offset tapPosition, double chartSize) {
    print('點擊位置: $tapPosition');

    // 檢查點擊的是哪個行星
    final CustomPainter painter = _getChartPainter();
    print('繪製器類型: ${painter.runtimeType}');

    // 先調用 hitTest 方法進行點擊檢測
    bool? hitResult = painter.hitTest(tapPosition);
    print('點擊結果: $hitResult');

    // 如果是法達盤，處理法達盤的點擊事件
    if (painter is FirdariaChartPainter) {
      final hitPeriod = painter.getLastHitPeriod();
      if (hitPeriod != null) {
        print('點中的法達盤週期: ${hitPeriod.majorPlanetName}');
        // 找到點擊的週期索引
        final index = widget.viewModel.firdariaData?.indexOf(hitPeriod) ?? -1;
        if (index >= 0) {
          // 更新選中的週期索引
          widget.viewModel.selectedFirdariaPeriodIndex = index;
          // 刷新頁面
          setState(() {});
        }
        return;
      }
    }

    PlanetPosition? hitPlanet;

    // 如果是 ChartPainter 類型，取得點擊的行星
    if (painter is ChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
      print('點中的行星 (ChartPainter): ${hitPlanet?.name}');
    }
    // 如果是 DualChartPainter 類型，取得點擊的行星
    else if (painter is DualChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
      print('點中的行星 (DualChartPainter): ${hitPlanet?.name}');
    }
    // 如果是 FirdariaChartPainter 類型，取得點擊的行星
    else if (painter is FirdariaChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
      print('點中的行星 (FirdariaChartPainter): ${hitPlanet?.name}');
    }

    // 如果找到行星，顯示其詳細信息
    if (hitPlanet != null) {
      print('顯示行星詳情: ${hitPlanet.name}');
      _showPlanetDetails(context, hitPlanet);
    } else {
      print('未找到行星或法達盤週期');
    }
  }

  /// 顯示行星詳細信息
  void _showPlanetDetails(BuildContext context, PlanetPosition planet) {
    print('_showPlanetDetails: planet = ${planet.name}');

    // 先移除現有的 overlay
    _removeOverlay();

    // 計算行星在畫面上的位置
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size size = renderBox.size;
    print('_showPlanetDetails: size = $size');

    // 獲取行星相位信息
    final aspects = widget.viewModel.chartData.aspects
            ?.where((aspect) =>
                (aspect.planet1.name == planet.name ||
                    aspect.planet2.name == planet.name) &&
                aspect.receptionType == ReceptionType.none)
            .toList() ??
        [];
    print('_showPlanetDetails: aspects.length = ${aspects.length}');

    // 獲取行星互容接納關係
    final receptions = widget.viewModel.chartData.aspects
            ?.where((aspect) =>
                (aspect.planet1.name == planet.name ||
                    aspect.planet2.name == planet.name) &&
                aspect.receptionType != ReceptionType.none)
            .toList() ??
        [];
    print('_showPlanetDetails: receptions.length = ${receptions.length}');

    // 計算卡片的位置，確保它不會超出畫面
    const double cardWidth = 280;
    const double cardHeight = 300; // 估計高度

    // 先將卡片放在畫面中央
    double left = (size.width - cardWidth) / 2;
    double top = (size.height - cardHeight) / 2;
    print('_showPlanetDetails: card position = ($left, $top)');

    try {
      // 創建並插入 overlay
      _overlayEntry = OverlayEntry(
        builder: (context) => Stack(
          children: [
            // 透明全屏底層，用於捕捉點擊事件
            Positioned.fill(
              child: GestureDetector(
                onTap: _removeOverlay, // 點擊空白處關閉卡片
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
            // 可拖動卡片
            DraggablePlanetDetailCard(
              planet: planet,
              aspects: aspects,
              receptions: receptions,
              viewModel: widget.viewModel,
              onClose: _removeOverlay,
              initialPosition: Offset(left, top),
            ),
          ],
        ),
      );

      // 插入 overlay
      Overlay.of(context).insert(_overlayEntry!);
      print('_showPlanetDetails: overlay inserted');

      // 更新最後點擊的行星
      setState(() {
        _lastHitPlanet = planet;
      });
    } catch (e) {
      print('_showPlanetDetails error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.viewModel.chartData.planets == null ||
        widget.viewModel.chartData.houses == null) {
      return const Center(child: CircularProgressIndicator());
    }
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    const tabBarHeight = 48.0; // TabBar 的標準高度
    final topPadding = MediaQuery.of(context).padding.top;
    final availableHeight =
        screenHeight - appBarHeight - tabBarHeight - topPadding - 20;
    final chartSize = availableHeight * 0.6; // 使用可用高度的 60%

    // 使用統一的佈局結構
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 10),
          Text(
            _getChartTitle(),
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),

          // 星盤圖
          SizedBox(
            height: chartSize,
            width: chartSize,
            child: GestureDetector(
              onTapDown: (TapDownDetails details) {
                _handleChartTap(context, details.localPosition, chartSize);
              },
              child: CustomPaint(
                painter: _getChartPainter(),
              ),
            ),
          ),

          const SizedBox(height: 10),

          // 時間調整控制元素
          TimeAdjustmentWidget(viewModel: widget.viewModel),

          const SizedBox(height: 10),

          // 界主星配置法按鈕
          ElevatedButton(
            onPressed: () => _showTermRulerProgression(context),
            child: const Text('界主星配置法'),
          ),

          const SizedBox(height: 10),

          // 使用模組化的人物信息組件，放在底部
          PersonInfoWidget(viewModel: widget.viewModel),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}

/// 可拖動的透明對話框組件
class _DraggableTransparentDialog extends StatefulWidget {
  final Widget child;
  final double maxWidth;

  const _DraggableTransparentDialog({
    Key? key,
    required this.child,
    required this.maxWidth,
  }) : super(key: key);

  @override
  State<_DraggableTransparentDialog> createState() => _DraggableTransparentDialogState();
}

class _DraggableTransparentDialogState extends State<_DraggableTransparentDialog> {
  Offset _position = Offset.zero;
  bool _isDragging = false;
  double _opacity = 1.0;

  @override
  void initState() {
    super.initState();
    // 初始位置設置為螢幕中央
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final screenSize = MediaQuery.of(context).size;
      setState(() {
        _position = Offset(
          (screenSize.width - widget.maxWidth) / 2,
          screenSize.height * 0.1, // 距離頂部 10%
        );
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        // 透明背景，點擊時關閉對話框
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Container(
            width: screenSize.width,
            height: screenSize.height,
            color: Colors.transparent,
          ),
        ),
        // 可拖動的對話框
        Positioned(
          left: _position.dx,
          top: _position.dy,
          child: GestureDetector(
            onPanStart: (details) {
              setState(() {
                _isDragging = true;
                _opacity = 0.7; // 拖動時變透明
              });
            },
            onPanUpdate: (details) {
              setState(() {
                _position += details.delta;

                // 限制在螢幕範圍內
                _position = Offset(
                  _position.dx.clamp(0, screenSize.width - widget.maxWidth),
                  _position.dy.clamp(0, screenSize.height - 100), // 保留底部空間
                );
              });
            },
            onPanEnd: (details) {
              setState(() {
                _isDragging = false;
                _opacity = 1.0; // 停止拖動時恢復不透明
              });
            },
            onTap: () {
              // 點擊對話框內容時切換透明度
              setState(() {
                _opacity = _opacity == 1.0 ? 0.3 : 1.0;
              });
            },
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _opacity,
              child: Container(
                width: widget.maxWidth,
                constraints: BoxConstraints(
                  maxHeight: screenSize.height * 0.8,
                ),
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Material(
                    borderRadius: BorderRadius.circular(12),
                    elevation: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _isDragging
                            ? Colors.blue.withOpacity(0.5)
                            : Colors.grey.withOpacity(0.3),
                          width: _isDragging ? 2 : 1,
                        ),
                      ),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        // 拖動提示（僅在第一次顯示時出現）
        if (!_isDragging && _position == Offset.zero)
          Positioned(
            right: 10, // 縮小右邊距
            top: 10, // 縮小上邊距
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // 縮小內邊距
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(15), // 縮小圓角
              ),
              child: const Text(
                '可拖動 • 點擊切換透明度',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10, // 縮小字體
                ),
              ),
            ),
          ),
      ],
    );
  }
}
